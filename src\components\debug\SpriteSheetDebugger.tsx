import React, { useState, useRef, useEffect } from 'react';
import { generateFrameCoordinates } from '../../utils/spriteSheetValidator';

/**
 * Visual debugger for sprite sheet cutting
 * Shows exactly where frames are being cut from the sprite sheet
 */
export default function SpriteSheetDebugger() {
  const [spriteUrl, setSpriteUrl] = useState('');
  const [showOverlay, setShowOverlay] = useState(true);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [imageLoaded, setImageLoaded] = useState(false);
  const [frames, setFrames] = useState<any[]>([]);

  const loadAndAnalyzeSprite = () => {
    if (!spriteUrl || !canvasRef.current) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const img = new Image();
    img.crossOrigin = 'anonymous';
    
    img.onload = () => {
      // Set canvas size to match image
      canvas.width = img.width;
      canvas.height = img.height;
      
      // Draw the original image
      ctx.drawImage(img, 0, 0);
      
      // Generate frame coordinates
      const frameCoords = generateFrameCoordinates(img.width, img.height, 5, 5);
      setFrames(frameCoords);
      
      // Draw frame overlays if enabled
      if (showOverlay) {
        drawFrameOverlays(ctx, frameCoords);
      }
      
      setImageLoaded(true);
      console.log('🖼️ Sprite sheet loaded:', img.width, 'x', img.height);
      console.log('📐 Frame coordinates:', frameCoords);
    };
    
    img.onerror = () => {
      console.error('❌ Failed to load sprite sheet');
      setImageLoaded(false);
    };
    
    img.src = spriteUrl;
  };

  const drawFrameOverlays = (ctx: CanvasRenderingContext2D, frameCoords: any[]) => {
    frameCoords.forEach((frame, index) => {
      // Draw frame border
      ctx.strokeStyle = index < 5 ? '#ff0000' : // Top row - red
                       index < 10 ? '#00ff00' : // Second row - green  
                       index < 15 ? '#0000ff' : // Middle row - blue
                       index < 20 ? '#ffff00' : // Fourth row - yellow
                       '#ff00ff'; // Bottom row - magenta
      ctx.lineWidth = 2;
      ctx.strokeRect(frame.x, frame.y, frame.width, frame.height);
      
      // Draw frame number
      ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
      ctx.fillRect(frame.x, frame.y, 30, 20);
      ctx.fillStyle = 'white';
      ctx.font = '12px Arial';
      ctx.fillText((index + 1).toString(), frame.x + 5, frame.y + 15);
      
      // Highlight bottom row frames
      if (index >= 20) {
        ctx.fillStyle = 'rgba(255, 0, 255, 0.2)';
        ctx.fillRect(frame.x, frame.y, frame.width, frame.height);
      }
    });
  };

  const toggleOverlay = () => {
    setShowOverlay(!showOverlay);
    if (imageLoaded) {
      loadAndAnalyzeSprite(); // Redraw
    }
  };

  const downloadDebugImage = () => {
    if (!canvasRef.current) return;
    
    const link = document.createElement('a');
    link.download = 'sprite-sheet-debug.png';
    link.href = canvasRef.current.toDataURL();
    link.click();
  };

  return (
    <div style={{ padding: '20px', maxWidth: '1200px', margin: '0 auto' }}>
      <h2 style={{ marginBottom: '20px' }}>🔍 Sprite Sheet Cutting Debugger</h2>
      
      {/* Controls */}
      <div style={{ 
        display: 'flex', 
        gap: '12px', 
        marginBottom: '20px',
        alignItems: 'center',
        flexWrap: 'wrap'
      }}>
        <input
          type="text"
          placeholder="Paste sprite sheet URL or base64 data URL here..."
          value={spriteUrl}
          onChange={(e) => setSpriteUrl(e.target.value)}
          style={{
            flex: 1,
            minWidth: '300px',
            padding: '8px 12px',
            border: '1px solid #ccc',
            borderRadius: '4px',
            fontSize: '14px'
          }}
        />
        <button
          onClick={loadAndAnalyzeSprite}
          style={{
            padding: '8px 16px',
            backgroundColor: '#3b82f6',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
            fontWeight: 'bold'
          }}
        >
          🔍 Analyze
        </button>
        <button
          onClick={toggleOverlay}
          style={{
            padding: '8px 16px',
            backgroundColor: showOverlay ? '#10b981' : '#6b7280',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          {showOverlay ? '👁️ Hide Overlay' : '👁️ Show Overlay'}
        </button>
        {imageLoaded && (
          <button
            onClick={downloadDebugImage}
            style={{
              padding: '8px 16px',
              backgroundColor: '#8b5cf6',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            💾 Download Debug
          </button>
        )}
      </div>

      {/* Instructions */}
      <div style={{ 
        backgroundColor: '#f0f9ff', 
        border: '1px solid #0ea5e9',
        borderRadius: '8px',
        padding: '16px',
        marginBottom: '20px',
        fontSize: '14px'
      }}>
        <h3 style={{ margin: '0 0 8px 0', color: '#0c4a6e' }}>📋 How to Use:</h3>
        <ol style={{ margin: 0, paddingLeft: '20px' }}>
          <li>Generate a sprite sheet using the Animation Symbol Generator</li>
          <li>Copy the generated image URL or convert to base64 and paste above</li>
          <li>Click "Analyze" to see the cutting overlay</li>
          <li>Check if the bottom row (magenta frames 21-25) are properly positioned</li>
          <li>Use the color-coded borders to identify which row each frame belongs to</li>
        </ol>
        <div style={{ marginTop: '12px', fontSize: '13px' }}>
          <strong>Color Code:</strong> 
          <span style={{ color: '#dc2626' }}> Red=Row1</span>,
          <span style={{ color: '#16a34a' }}> Green=Row2</span>,
          <span style={{ color: '#2563eb' }}> Blue=Row3</span>,
          <span style={{ color: '#ca8a04' }}> Yellow=Row4</span>,
          <span style={{ color: '#c026d3' }}> Magenta=Row5(Bottom)</span>
        </div>
      </div>

      {/* Canvas */}
      <div style={{ 
        border: '2px solid #e5e7eb',
        borderRadius: '8px',
        padding: '16px',
        backgroundColor: '#f9fafb',
        textAlign: 'center'
      }}>
        <canvas
          ref={canvasRef}
          style={{
            maxWidth: '100%',
            height: 'auto',
            border: '1px solid #d1d5db',
            borderRadius: '4px',
            backgroundColor: 'white'
          }}
        />
        {!imageLoaded && (
          <div style={{ 
            padding: '40px',
            color: '#6b7280',
            fontSize: '16px'
          }}>
            📷 Load a sprite sheet to see the cutting visualization
          </div>
        )}
      </div>

      {/* Frame Analysis */}
      {frames.length > 0 && (
        <div style={{ 
          marginTop: '20px',
          backgroundColor: '#f3f4f6',
          border: '1px solid #d1d5db',
          borderRadius: '8px',
          padding: '16px'
        }}>
          <h3 style={{ marginBottom: '12px' }}>📊 Frame Analysis</h3>
          <div style={{ 
            display: 'grid', 
            gridTemplateColumns: 'repeat(5, 1fr)',
            gap: '8px',
            fontSize: '12px',
            fontFamily: 'monospace'
          }}>
            {frames.map((frame, index) => (
              <div
                key={index}
                style={{
                  padding: '8px',
                  backgroundColor: index >= 20 ? '#fdf2f8' : 'white',
                  border: `1px solid ${index >= 20 ? '#ec4899' : '#d1d5db'}`,
                  borderRadius: '4px'
                }}
              >
                <div style={{ fontWeight: 'bold' }}>Frame {index + 1}</div>
                <div>({frame.x},{frame.y})</div>
                <div>{frame.width}×{frame.height}</div>
                <div style={{ fontSize: '10px', color: '#6b7280' }}>
                  Row {frame.row + 1}, Col {frame.col + 1}
                </div>
              </div>
            ))}
          </div>
          
          {/* Bottom Row Check */}
          <div style={{
            marginTop: '16px',
            padding: '12px',
            backgroundColor: frames.some(f => f.row === 4 && (f.y + f.height) > 1008) ? '#fee2e2' : '#dcfce7',
            border: `1px solid ${frames.some(f => f.row === 4 && (f.y + f.height) > 1008) ? '#ef4444' : '#16a34a'}`,
            borderRadius: '6px'
          }}>
            <strong>Bottom Row Check (16px margin):</strong> {
              frames.some(f => f.row === 4 && (f.y + f.height) > 1008)
                ? '❌ Bottom row may be cut off!'
                : '✅ Bottom row positioning looks good'
            }
            <div style={{ fontSize: '12px', marginTop: '4px', opacity: 0.8 }}>
              Expected bottom row max Y: 1004px (with 20px bottom margin)
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
