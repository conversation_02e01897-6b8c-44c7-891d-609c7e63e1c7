{"root": ["./src/app.tsx", "./src/premiumentry.tsx", "./src/main.tsx", "./src/store.ts", "./src/swiper-test.tsx", "./src/types.ts", "./src/vite-env.d.ts", "./src/components/aitools.tsx", "./src/components/apisetup.tsx", "./src/components/apistatusindicator.tsx", "./src/components/advanced.tsx", "./src/components/analyticssetup.tsx", "./src/components/audiocomponent.tsx", "./src/components/basicsetup.tsx", "./src/components/betlinessetup.tsx", "./src/components/bonusfeatures.tsx", "./src/components/certification.tsx", "./src/components/clusterpreview.tsx", "./src/components/configmodal.tsx", "./src/components/coremechanics.tsx", "./src/components/dashboard.tsx", "./src/components/documentation.tsx", "./src/components/emergencynavigation.tsx", "./src/components/enhancedgamecrafterdashboard.tsx", "./src/components/errorboundary.tsx", "./src/components/features.tsx", "./src/components/gamecanvasdemo.tsx", "./src/components/gamecrafterdashboard.tsx", "./src/components/gamecraftertheme.tsx", "./src/components/gamerules.tsx", "./src/components/gameselectionmodal.tsx", "./src/components/gametypeselector.tsx", "./src/components/gametypessection.tsx", "./src/components/loadlimiter.tsx", "./src/components/localization.tsx", "./src/components/loginscreen.tsx", "./src/components/magicboxpage.tsx", "./src/components/mathmodel.tsx", "./src/components/mobileoptimization.tsx", "./src/components/mockupprogressbar.tsx", "./src/components/playerexperience.tsx", "./src/components/premiumapp.tsx", "./src/components/premiumlayout.tsx", "./src/components/premiumslotpreview.tsx", "./src/components/progressbar.tsx", "./src/components/reelanimation.tsx", "./src/components/reeltest.tsx", "./src/components/refinedapp.tsx", "./src/components/refinedappnavigation.tsx", "./src/components/simplifiedapp.tsx", "./src/components/slotcreator.tsx", "./src/components/splashscreen.tsx", "./src/components/stepnavigation.tsx", "./src/components/symbolprocessor.tsx", "./src/components/symbolspayouts.tsx", "./src/components/symbolspaytable.tsx", "./src/components/theme.tsx", "./src/components/themegenerator.tsx", "./src/components/themepreview.tsx", "./src/components/uidesign.tsx", "./src/components/uitheme.tsx", "./src/components/unifiedappnavigation.tsx", "./src/components/animation-lab/animationlab.tsx", "./src/components/animation-lab/animationlab2.tsx", "./src/components/animation-lab/animationlabdemo.tsx", "./src/components/animation-lab/animationlabmodeprovider.tsx", "./src/components/animation-lab/animationtestlab.tsx", "./src/components/animation-lab/enhancedanimationlab.tsx", "./src/components/animation-lab/step4pixipreview.tsx", "./src/components/animation-lab/universalanimationengine.tsx", "./src/components/animation-lab/index.ts", "./src/components/animation-lab/ai/aianalysisvalidator.ts", "./src/components/animation-lab/ai/aiimageanalyzer.ts", "./src/components/animation-lab/ai/gptvisionclient.ts", "./src/components/animation-lab/animation/gsapanimationmanager.ts", "./src/components/animation-lab/components/aiconfigpanel.tsx", "./src/components/animation-lab/components/aigenerationpanel.tsx", "./src/components/animation-lab/components/advancedgsapanimator.tsx", "./src/components/animation-lab/components/animationcomposer.tsx", "./src/components/animation-lab/components/animationselectionmodal.tsx", "./src/components/animation-lab/components/autoanimatesection.tsx", "./src/components/animation-lab/components/canvasworkspace.tsx", "./src/components/animation-lab/components/creationmodeselector.tsx", "./src/components/animation-lab/components/exportsection.tsx", "./src/components/animation-lab/components/fileuploadsystem.tsx", "./src/components/animation-lab/components/imageuploader.tsx", "./src/components/animation-lab/components/interactivecanvas.tsx", "./src/components/animation-lab/components/keptspritesmanager.tsx", "./src/components/animation-lab/components/modetoggleheader.tsx", "./src/components/animation-lab/components/previewsystem.tsx", "./src/components/animation-lab/components/professionalatlaspreview.tsx", "./src/components/animation-lab/components/professionaltimeline.tsx", "./src/components/animation-lab/components/quickpresets.tsx", "./src/components/animation-lab/components/simpleaianalyzer.tsx", "./src/components/animation-lab/components/simpleanimationcanvas.tsx", "./src/components/animation-lab/components/simplepreview.tsx", "./src/components/animation-lab/components/symbolcompositionstudio.tsx", "./src/components/animation-lab/components/transformcontrols.tsx", "./src/components/animation-lab/components/enhancedthemeexplorer/themeoptions.ts", "./src/components/animation-lab/components/step-2/gametypecard.tsx", "./src/components/animation-lab/components/step-2/gametypes.ts", "./src/components/animation-lab/core/animationengine.ts", "./src/components/animation-lab/core/assetmanager.ts", "./src/components/animation-lab/core/errorhandler.ts", "./src/components/animation-lab/core/imageanalyzer.ts", "./src/components/animation-lab/core/projectmanager.ts", "./src/components/animation-lab/core/spritemanager.ts", "./src/components/animation-lab/core/uicontroller.ts", "./src/components/animation-lab/modes/advancedmode.tsx", "./src/components/animation-lab/modes/simplemode.tsx", "./src/components/animation-lab/utils/formatconverter.ts", "./src/components/animation-lab/utils/imageprocessor.ts", "./src/components/canvas/gamecanvascontainer.tsx", "./src/components/canvas/interactivegamecanvas.tsx", "./src/components/canvas/propertypanel.tsx", "./src/components/canvas/index.ts", "./src/components/common/optimizedpixidemo.tsx", "./src/components/common/pixicontainer.tsx", "./src/components/common/withanimationframetracking.tsx", "./src/components/direct-steps/apiexport.tsx", "./src/components/direct-steps/analyticssimulation.tsx", "./src/components/direct-steps/marketcompliance.tsx", "./src/components/enhanced-animation-lab/layout-animation-template.tsx", "./src/components/enhanced-animation-lab/workspace-pannel.tsx", "./src/components/game-canvas/canvascontrols.tsx", "./src/components/game-canvas/gamecanvas.tsx", "./src/components/game-canvas/gamecanvasworkspace.tsx", "./src/components/game-canvas/layerpanel.tsx", "./src/components/game-canvas/layersystem.tsx", "./src/components/game-canvas/propertypanel.tsx", "./src/components/game-canvas/gamecanvasstore.ts", "./src/components/game-canvas/index.ts", "./src/components/layout/premiumlayout.tsx", "./src/components/layout/sidebarcontext.tsx", "./src/components/marketing/gamecraftermarketing.tsx", "./src/components/mockups/csspreviewwrapper.tsx", "./src/components/mockups/cssslotmockup.tsx", "./src/components/mockups/index.ts", "./src/components/mockups/mockuputils.ts", "./src/components/navigation/verticalstepsidebar.tsx", "./src/components/scratch-journey/scratchcardjourney.tsx", "./src/components/shared/aiassistant.tsx", "./src/components/shared/performancemonitor.tsx", "./src/components/shared/premiumslotpreview.tsx", "./src/components/shared/profilemanager.tsx", "./src/components/shared/usabilitytracker.tsx", "./src/components/slot-engine/premiumslotengine.tsx", "./src/components/slot-engine/unifiedslotpreview.tsx", "./src/components/slot-engine/index.ts", "./src/components/slot-visualization/animationintegrator.tsx", "./src/components/slot-visualization/enhancedslotmachine.tsx", "./src/components/slot-visualization/premiumslotmachine.tsx", "./src/components/slot-visualization/reelanimation.tsx", "./src/components/slot-visualization/reelbuilder3d.tsx", "./src/components/slot-visualization/reeltest.tsx", "./src/components/slot-visualization/slotgameui.tsx", "./src/components/slot-visualization/slotmachinedemo.tsx", "./src/components/slot-visualization/slotpixidemo.tsx", "./src/components/slot-visualization/tier1pixislot.tsx", "./src/components/slot-visualization/index.ts", "./src/components/ui/brandlogo.tsx", "./src/components/visual-journey/colorcustomizer.tsx", "./src/components/visual-journey/debuglogger.tsx", "./src/components/visual-journey/deepsimulation.tsx", "./src/components/visual-journey/directdebuglogger.tsx", "./src/components/visual-journey/enhancedgametypeselector.tsx", "./src/components/visual-journey/fixednavigationhandler.tsx", "./src/components/visual-journey/forcenavigator.tsx", "./src/components/visual-journey/marketcompliance.tsx", "./src/components/visual-journey/mathautomation.tsx", "./src/components/visual-journey/mathautomationcomponents.tsx", "./src/components/visual-journey/mockupsymbolgenerator.tsx", "./src/components/visual-journey/navigationlogger.tsx", "./src/components/visual-journey/paylinepreview.tsx", "./src/components/visual-journey/progressindicator.tsx", "./src/components/visual-journey/reelbuilder.tsx", "./src/components/visual-journey/simplestepnavigator.tsx", "./src/components/visual-journey/step1_enhancedthemeexplorer.tsx", "./src/components/visual-journey/step2_gametypeselector.tsx", "./src/components/visual-journey/stepnavigationfix.tsx", "./src/components/visual-journey/symbolcardmockup.tsx", "./src/components/visual-journey/symbolgenerator.tsx", "./src/components/visual-journey/themeexplorer.tsx", "./src/components/visual-journey/visualjourney.tsx", "./src/components/visual-journey/visualmathlab.tsx", "./src/components/visual-journey/background/backgroundcreator.tsx", "./src/components/visual-journey/contexts/slotgamecontext.tsx", "./src/components/visual-journey/debug/patterndebug.tsx", "./src/components/visual-journey/debugging/debugnavigationtracker.tsx", "./src/components/visual-journey/debugging/qadebugmodule.tsx", "./src/components/visual-journey/game-frame/gameframedesigner.tsx", "./src/components/visual-journey/game-frame/gameframedesignerwithswiper.tsx", "./src/components/visual-journey/grid-preview/gridpreviewwrapper.tsx", "./src/components/visual-journey/grid-preview/landscapegridpreview.tsx", "./src/components/visual-journey/grid-preview/portraitgridpreview.tsx", "./src/components/visual-journey/grid-preview/premiumgridpreviewinjector.tsx", "./src/components/visual-journey/grid-preview/premiumslotpreviewblock.tsx", "./src/components/visual-journey/grid-preview/professionalunifiedgridpreview.tsx", "./src/components/visual-journey/grid-preview/purepixigridpreview.tsx", "./src/components/visual-journey/grid-preview/step5previewwrapper.tsx", "./src/components/visual-journey/grid-preview/symbolpreviewwrapper.tsx", "./src/components/visual-journey/grid-preview/unifiedgridpreview.tsx", "./src/components/visual-journey/grid-preview/index.ts", "./src/components/visual-journey/shared/fileuploadbutton.tsx", "./src/components/visual-journey/shared/gameiddisplay.tsx", "./src/components/visual-journey/shared/interactivelogopositioner.tsx", "./src/components/visual-journey/shared/loadingjourneystore.ts", "./src/components/visual-journey/shared/professionalloadingpreview.tsx", "./src/components/visual-journey/shared/symbolgrid.tsx", "./src/components/visual-journey/slot-animation/blackbarui.tsx", "./src/components/visual-journey/slot-animation/directspincontroller.tsx", "./src/components/visual-journey/slot-animation/endlessreelpreview.tsx", "./src/components/visual-journey/slot-animation/glowfilter.ts", "./src/components/visual-journey/slot-animation/minimalreproduction.tsx", "./src/components/visual-journey/slot-animation/mobilelandscapeui.tsx", "./src/components/visual-journey/slot-animation/mobileportraitui.tsx", "./src/components/visual-journey/slot-animation/particleeffect.ts", "./src/components/visual-journey/slot-animation/pixislotmachine.tsx", "./src/components/visual-journey/slot-animation/pixislotpreview.tsx", "./src/components/visual-journey/slot-animation/professionalcssslot.tsx", "./src/components/visual-journey/slot-animation/purepixiui.tsx", "./src/components/visual-journey/slot-animation/simpleslotmachineintegration.tsx", "./src/components/visual-journey/slot-animation/slotbottombar.tsx", "./src/components/visual-journey/slot-animation/slotgameui.tsx", "./src/components/visual-journey/slot-animation/slotmachineintegration.tsx", "./src/components/visual-journey/slot-animation/stepawarepremiumslotpreview.tsx", "./src/components/visual-journey/slot-animation/slottypes.ts", "./src/components/visual-journey/slot-animation/filters/glowfilter.ts", "./src/components/visual-journey/steps/enhancedstep1_themeselection.fix.tsx", "./src/components/visual-journey/steps/enhancedstep1_themeselection.fixed.tsx", "./src/components/visual-journey/steps/enhancedstep1_themeselection.tsx", "./src/components/visual-journey/steps/enhancedstep2_gametypeselector.tsx", "./src/components/visual-journey/steps/finalsymbolgrid.tsx", "./src/components/visual-journey/steps/step10_deepsimulation.tsx", "./src/components/visual-journey/steps/step11_marketcompliance.tsx", "./src/components/visual-journey/steps/step12_apiexport.tsx", "./src/components/visual-journey/steps/step1_themeselection.tsx", "./src/components/visual-journey/steps/step2_gametypeselector.tsx", "./src/components/visual-journey/steps/step2_symbolgeneration.tsx", "./src/components/visual-journey/steps/step3_reelconfiguration.tsx", "./src/components/visual-journey/steps/step4_symbolcreation.tsx", "./src/components/visual-journey/steps/step4_symbolgeneration.tsx", "./src/components/visual-journey/steps/step4_symbolgeneration_backup.tsx", "./src/components/visual-journey/steps/step5_gameassets.tsx", "./src/components/visual-journey/steps/step5_gameframedesigner.tsx", "./src/components/visual-journey/steps/step5_gameframedesignerswiper.tsx", "./src/components/visual-journey/steps/step5_symbolanimation.tsx", "./src/components/visual-journey/steps/step6_animationstudio.tsx", "./src/components/visual-journey/steps/step6_animationstudiointegration.tsx", "./src/components/visual-journey/steps/step6_backgroundcreator.tsx", "./src/components/visual-journey/steps/step6_gameassets.tsx", "./src/components/visual-journey/steps/step7_animationstudiointegration.tsx", "./src/components/visual-journey/steps/step7_winanimationworkshop.tsx", "./src/components/visual-journey/steps/step8_bonusfeatures.tsx", "./src/components/visual-journey/steps/step8_splashpreloader.tsx", "./src/components/visual-journey/steps/step8_winanimationworkshop.tsx", "./src/components/visual-journey/steps/step9_loadingexperience.tsx", "./src/components/visual-journey/steps/step9_mathlab.tsx", "./src/components/visual-journey/win-animation/advancedanimationcontrols.tsx", "./src/components/visual-journey/win-animation/advancedanimationworkshop.tsx", "./src/components/visual-journey/win-animation/animationcontrols.tsx", "./src/components/visual-journey/win-animation/animationpresets.tsx", "./src/components/visual-journey/win-animation/customwinhighlighteffect.ts", "./src/components/visual-journey/win-animation/emulatedspinsystem.tsx", "./src/components/visual-journey/win-animation/enhancedwinanimationworkshop.tsx", "./src/components/visual-journey/win-animation/lightningtestcomponent.tsx", "./src/components/visual-journey/win-animation/previewreelcontroller.ts", "./src/components/visual-journey/win-animation/symbolhighlightpreview.tsx", "./src/components/visual-journey/win-animation/themeanimationpresets.tsx", "./src/components/visual-journey/win-animation/updatedwinanimationworkshop.tsx", "./src/components/visual-journey/win-animation/winanimationworkshop.tsx", "./src/engine/gameengine.ts", "./src/engine/index.ts", "./src/engine/utils.ts", "./src/engine/ai/aielementdetector.ts", "./src/engine/ai/mlsegmentationengine.ts", "./src/engine/animation/advancedskeletalsystem.ts", "./src/engine/animation/meshdeformationengine.ts", "./src/engine/animation/professionalanimationcurves.ts", "./src/engine/animation/sophisticatedanimationorchestrator.ts", "./src/engine/config/animationpresets.ts", "./src/engine/config/defaultconfig.ts", "./src/engine/core/eventbus.ts", "./src/engine/core/slotengine.ts", "./src/engine/core/statemanager.ts", "./src/engine/core/interfaces.ts", "./src/engine/managers/animationmanager.ts", "./src/engine/managers/assetmanager.ts", "./src/engine/managers/audiomanager.ts", "./src/engine/modules/animationmanager.ts", "./src/engine/modules/reelmanager.ts", "./src/engine/modules/spinmanager.ts", "./src/engine/modules/symbolpool.ts", "./src/engine/modules/winevaluator.ts", "./src/engine/physics/advancedphysicsengine.ts", "./src/engine/physics/physicsrelationshipengine.ts", "./src/engine/pixi/professionalreelstrip.ts", "./src/engine/pixi/professionalslotmachine.ts", "./src/engine/pixi/slotscene.ts", "./src/engine/pixi/symbolpool.ts", "./src/engine/pixi/index.ts", "./src/engine/rendering/renderer.broken.ts", "./src/engine/rendering/renderer.fixed.ts", "./src/engine/rendering/renderer.ts", "./src/engine/rgs/rgsclient.ts", "./src/engine/types/types.ts", "./src/hooks/useasyncdata.ts", "./src/hooks/usepixiapp.ts", "./src/hooks/usepixislotanimations.ts", "./src/hooks/useslotlayout.ts", "./src/store/automationstore.ts", "./src/stores/animationlabstore.ts", "./src/stores/gamestore.ts", "./src/stores/sidebarstore.ts", "./src/utils/adaptivebuttondetection.ts", "./src/utils/advancededgedetection.ts", "./src/utils/aianimationengine.ts", "./src/utils/aibonesystem.ts", "./src/utils/aiwingsegmentation.ts", "./src/utils/animationenhancements.ts", "./src/utils/animationexporter.ts", "./src/utils/animationlabstorage.ts", "./src/utils/apiclient.ts", "./src/utils/apitypes.ts", "./src/utils/autoriggingengine.ts", "./src/utils/automatedpixirenderer.ts", "./src/utils/borderdetectionsystem.ts", "./src/utils/bulletproofspritedetector.ts", "./src/utils/colorsegmentation.ts", "./src/utils/configdefaults.ts", "./src/utils/coordinateextractor.ts", "./src/utils/devicedetection.ts", "./src/utils/enhancedopenaiclient.ts", "./src/utils/expandableatlas.ts", "./src/utils/foldercreator.ts", "./src/utils/gptclient.ts", "./src/utils/gptspriterecreation.ts", "./src/utils/gptvisionclient.ts", "./src/utils/gptvisionspritedetector.ts", "./src/utils/gsapmanager.ts", "./src/utils/imageprocessing.ts", "./src/utils/imagesaver.ts", "./src/utils/letterderivationsystem.ts", "./src/utils/manualletterextractor.ts", "./src/utils/manualswordextraction.ts", "./src/utils/meshprocessingworker.ts", "./src/utils/mocksounds.ts", "./src/utils/mockupservice.ts", "./src/utils/morphologicaloperations.ts", "./src/utils/multialgorithmfusion.ts", "./src/utils/multivisionclient.ts", "./src/utils/opencvboundingrefinement.ts", "./src/utils/pixelperfectboundingbox.ts", "./src/utils/pixiresourcemanager.ts", "./src/utils/professionalanimationengine.ts", "./src/utils/professionalanimationtimeline.ts", "./src/utils/professionalexportsystem.ts", "./src/utils/professionalgsapanimator.ts", "./src/utils/professionallayerextractor.ts", "./src/utils/professionalmeshprocessor.ts", "./src/utils/professionalperformancemonitor.ts", "./src/utils/professionalpixirenderer.ts", "./src/utils/professionalspriteatlas.ts", "./src/utils/realtimeperformancemonitor.ts", "./src/utils/simpleanimationengine.ts", "./src/utils/spritedecomposer.ts", "./src/utils/stepstorage.ts", "./src/utils/symbolstorage.ts", "./src/utils/textindividualization.ts", "./src/utils/universalanimationdetection.ts", "./src/utils/universalanimationengine.ts", "./src/utils/universalspritedetector.ts", "./src/utils/visualanimationrenderer.ts", "./src/utils/webworkermanager.ts"], "errors": true, "version": "5.8.3"}