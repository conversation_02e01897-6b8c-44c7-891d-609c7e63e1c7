<!DOCTYPE html>
<html>
<head>
    <title>Emergency GPT-4 Vision Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #1a1a1a; color: white; }
        .container { max-width: 800px; margin: 0 auto; }
        button { padding: 10px 20px; margin: 10px; background: #4CAF50; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #45a049; }
        .error { color: #ff6b6b; }
        .success { color: #51cf66; }
        .debug { background: #2d2d2d; padding: 10px; border-radius: 4px; margin: 10px 0; font-family: monospace; }
        input[type="file"] { margin: 10px 0; }
        img { max-width: 300px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔥 Emergency GPT-4 Vision Test</h1>
        <p>This is a simple HTML page to test GPT-4 Vision without React complications.</p>
        
        <input type="file" id="imageInput" accept="image/*" />
        <button onclick="testGPTVision()">🔥 Test GPT-4 Vision</button>
        
        <div id="imagePreview"></div>
        <div id="results"></div>
    </div>

    <script>
        const API_KEY = '********************************************************************************************************************************************************************';
        
        let selectedImage = null;
        
        document.getElementById('imageInput').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    selectedImage = e.target.result;
                    document.getElementById('imagePreview').innerHTML = 
                        `<img src="${selectedImage}" alt="Selected image">`;
                };
                reader.readAsDataURL(file);
            }
        });
        
        async function testGPTVision() {
            if (!selectedImage) {
                alert('Please select an image first!');
                return;
            }
            
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="debug">🔥 Starting emergency GPT-4 Vision test...</div>';
            
            try {
                console.log('🔥 EMERGENCY TEST: Starting direct API call...');
                
                const response = await fetch('https://api.openai.com/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${API_KEY}`
                    },
                    body: JSON.stringify({
                        model: "gpt-4o",
                        messages: [
                            {
                                role: "user",
                                content: [
                                    {
                                        type: "text",
                                        text: `Analyze this symbol and return JSON with these exact properties:
{
  "description": "clear description of what you see",
  "symbolType": "creature|gem|crystal|magical-tool|other",
  "confidence": 0.95
}

Return ONLY the JSON object, no other text.`
                                    },
                                    {
                                        type: "image_url",
                                        image_url: {
                                            url: selectedImage
                                        }
                                    }
                                ]
                            }
                        ],
                        max_tokens: 500,
                        temperature: 0.1
                    })
                });
                
                console.log('🔥 Response status:', response.status);
                
                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`API Error ${response.status}: ${errorText}`);
                }
                
                const data = await response.json();
                console.log('🔥 API Response:', data);
                
                const content = data.choices[0]?.message?.content;
                if (!content) {
                    throw new Error('No content in response');
                }
                
                // Try to parse the JSON
                let result;
                try {
                    result = JSON.parse(content.trim());
                } catch (parseError) {
                    // If JSON parsing fails, show the raw content
                    result = { rawContent: content };
                }
                
                resultsDiv.innerHTML = `
                    <div class="success">✅ GPT-4 Vision SUCCESS!</div>
                    <div class="debug">
                        <h3>Results:</h3>
                        <pre>${JSON.stringify(result, null, 2)}</pre>
                    </div>
                    <div class="debug">
                        <h3>Raw Response:</h3>
                        <pre>${content}</pre>
                    </div>
                `;
                
            } catch (error) {
                console.error('🔥 EMERGENCY TEST FAILED:', error);
                resultsDiv.innerHTML = `
                    <div class="error">❌ GPT-4 Vision FAILED!</div>
                    <div class="debug">
                        <h3>Error:</h3>
                        <pre>${error.message}</pre>
                    </div>
                `;
            }
        }
        
        // Test API key on load
        console.log('🔥 API Key loaded:', API_KEY.substring(0, 20) + '...');
    </script>
</body>
</html>