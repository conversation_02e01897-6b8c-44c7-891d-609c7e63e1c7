<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>SlotAI Step Navigation API</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    
    h1, h2, h3 {
      color: #E60012;
    }
    
    .endpoint {
      margin-bottom: 30px;
      padding: 20px;
      background-color: #f5f5f5;
      border-radius: 8px;
    }
    
    code {
      background-color: #f0f0f0;
      padding: 3px 5px;
      border-radius: 3px;
      font-family: SFMono-Regular, <PERSON>solas, "Liberation Mono", Menlo, monospace;
    }
    
    pre {
      background-color: #f0f0f0;
      padding: 15px;
      border-radius: 5px;
      overflow-x: auto;
    }
    
    button {
      background-color: #E60012;
      color: white;
      border: none;
      padding: 10px 15px;
      border-radius: 4px;
      cursor: pointer;
      font-weight: bold;
      margin-right: 10px;
      margin-bottom: 10px;
    }
    
    button:hover {
      background-color: #cc0010;
    }
    
    .response {
      margin-top: 15px;
      padding: 15px;
      background-color: #f0f0f0;
      border-radius: 5px;
      display: none;
    }
    
    .controls {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      margin-bottom: 20px;
    }
  </style>
</head>
<body>
  <h1>SlotAI Step Navigation API</h1>
  <p>
    This page provides direct access to navigate between steps in the SlotAI application.
    Use the buttons below to trigger navigation to specific steps.
  </p>
  
  <div class="controls">
    <button onclick="directNavigate(0)">Go to Step 1</button>
    <button onclick="directNavigate(1)">Go to Step 2</button>
    <button onclick="directNavigate(2)">Go to Step 3</button>
    <button onclick="directNavigate(3)">Go to Step 4</button>
    <button onclick="directNavigate(4)">Go to Step 5</button>
    <button onclick="directNavigate(5)">Go to Step 6</button>
    <button onclick="directNavigate(6)">Go to Step 7</button>
  </div>
  
  <div class="endpoint">
    <h2>Direct Step Navigation</h2>
    <p>Use this endpoint to navigate directly to a specific step.</p>
    
    <pre><code>GET /?step={stepIndex}&force=true</code></pre>
    
    <p>Parameters:</p>
    <ul>
      <li><code>step</code> - The zero-based index of the step to navigate to (0 = Step 1, 1 = Step 2, etc.)</li>
      <li><code>force</code> - Set to "true" to force navigation regardless of current step</li>
    </ul>
    
    <button onclick="executeDirectNavigation()">Execute Direct Navigation</button>
    
    <div id="directResponse" class="response">
      <pre></pre>
    </div>
  </div>
  
  <div class="endpoint">
    <h2>Data Management</h2>
    <p>Tools for managing SlotAI data state</p>
    
    <div class="controls">
      <button onclick="clearLocalStorage()">Clear All Data</button>
      <button onclick="showCurrentState()">Show Current State</button>
    </div>
    
    <div id="dataResponse" class="response">
      <pre></pre>
    </div>
  </div>
  
  <div class="endpoint">
    <h2>Emergency Navigation Options</h2>
    <p>Use these options if direct navigation isn't working</p>
    
    <div class="controls">
      <button onclick="window.location.href='/standalone-step2.html'">Go to Standalone Step 2</button>
      <button onclick="window.location.href='/?loadDebug=true'">Load with Debug Mode</button>
    </div>
  </div>
  
  <script>
    // Navigate to a specific step
    function directNavigate(step) {
      window.location.href = `/?step=${step}&force=true&t=${Date.now()}`;
    }
    
    // Execute direct navigation with custom parameters
    function executeDirectNavigation() {
      const step = prompt("Enter step number (0-based index):", "1");
      if (step === null) return;
      
      directNavigate(parseInt(step, 10));
    }
    
    // Clear all localStorage data
    function clearLocalStorage() {
      if (confirm("This will clear all saved data. Continue?")) {
        localStorage.clear();
        
        const response = document.getElementById('dataResponse');
        response.style.display = 'block';
        response.querySelector('pre').textContent = "All localStorage data cleared successfully.";
      }
    }
    
    // Show current state
    function showCurrentState() {
      const response = document.getElementById('dataResponse');
      response.style.display = 'block';
      
      try {
        const data = {
          emergency_nav: localStorage.getItem('slotai_emergency_nav'),
          target_step: localStorage.getItem('slotai_target_step'),
          game_data: JSON.parse(localStorage.getItem('slotai_game_data') || '{}'),
          timestamp: localStorage.getItem('slotai_timestamp'),
          all_keys: Object.keys(localStorage)
        };
        
        response.querySelector('pre').textContent = JSON.stringify(data, null, 2);
      } catch (e) {
        response.querySelector('pre').textContent = "Error reading state: " + e.message;
      }
    }
  </script>
</body>
</html>