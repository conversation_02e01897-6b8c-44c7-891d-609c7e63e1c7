// Layout Template Definitions
interface LayoutTemplate {
  id: 'text-top' | 'text-bottom' | 'text-overlay';
  name: string;
  description: string;
  icon: string;
  applyLayout: (sprites: any[], workspaceWidth: number, workspaceHeight: number) => any[];
}

// Animation Template Definitions  
interface AnimationTemplate {
  id: 'bounce' | 'pulse' | 'glow' | 'rotate' | 'shake' | 'sparkle' | 'flash' | 'wave';
  name: string;
  description: string;
  icon: string;
  duration: number;
  applyAnimation: (sprites: any[]) => void;
}

// Layout Template Configurations
export const LAYOUT_TEMPLATES: LayoutTemplate[] = [
  {
    id: 'text-top',
    name: 'Text on Top',
    description: 'Text above, symbol below',
    icon: '🔤',
    applyLayout: (sprites, workspaceWidth, workspaceHeight) => {
      const symbolSprites = sprites.filter(s => s.type === 'symbol' || s.type === 'element');
      const letterSprites = sprites.filter(s => s.type === 'letter');
      
      const result = [];
      
      // Position main symbol in center-lower area preserving original bounds and aspect ratio
      symbolSprites.forEach((sprite, index) => {
        // PRESERVE ORIGINAL ASPECT RATIO AND BOUNDS instead of forcing square
        const maxSize = Math.min(workspaceWidth * 0.5, workspaceHeight * 0.45);
        
        // Validate sprite dimensions to prevent NaN calculations
        const validWidth = (sprite.width && !isNaN(sprite.width) && sprite.width > 0) ? sprite.width : 100;
        const validHeight = (sprite.height && !isNaN(sprite.height) && sprite.height > 0) ? sprite.height : 100;
        const aspectRatio = validWidth / validHeight;
        
        console.log(`📐 text-top sprite ${index}: width=${sprite.width} -> ${validWidth}, height=${sprite.height} -> ${validHeight}, ratio=${aspectRatio}`);
        
        let newWidth, newHeight;
        if (aspectRatio > 1) {
          // Wider than tall - limit by width
          newWidth = Math.min(maxSize, validWidth);
          newHeight = newWidth / aspectRatio;
        } else {
          // Taller than wide - limit by height  
          newHeight = Math.min(maxSize, validHeight);
          newWidth = newHeight * aspectRatio;
        }
        
        // DISTINCT POSITIONING: Arrange symbols in a vertical column in center-lower area
        const symbolSpacing = Math.max(15, workspaceHeight * 0.02);
        const totalSymbolsHeight = (symbolSprites.length * newHeight) + ((symbolSprites.length - 1) * symbolSpacing);
        const startY = workspaceHeight * 0.6 - (totalSymbolsHeight / 2);
        
        result.push({
          ...sprite,
          x: (workspaceWidth - newWidth) / 2,
          y: startY + (index * (newHeight + symbolSpacing)),
          width: newWidth,
          height: newHeight
        });
      });
      
      // Position letters closer to symbol preserving original bounds
      if (letterSprites.length > 0) {
        // PRESERVE ORIGINAL LETTER BOUNDS instead of forcing uniform sizing
        const maxLetterHeight = workspaceHeight * 0.15;
        const letterSpacing = Math.max(20, workspaceWidth * 0.03);
        
        // Calculate total width needed preserving individual letter sizes
        const totalLetterWidth = letterSprites.reduce((total, sprite) => {
          const validHeight = (sprite.height && !isNaN(sprite.height) && sprite.height > 0) ? sprite.height : 50;
          const validWidth = (sprite.width && !isNaN(sprite.width) && sprite.width > 0) ? sprite.width : 50;
          const scaledHeight = Math.min(maxLetterHeight, validHeight);
          const scaledWidth = (validWidth / validHeight) * scaledHeight;
          console.log(`📐 text-top letter total calc: ${sprite.width}->${validWidth}, ${sprite.height}->${validHeight}, scaled: ${scaledWidth}x${scaledHeight}`);
          return total + scaledWidth;
        }, 0);
        const totalSpacingWidth = (letterSprites.length - 1) * letterSpacing;
        const totalTextWidth = totalLetterWidth + totalSpacingWidth;
        
        let currentX = (workspaceWidth - totalTextWidth) / 2;
        
        // Position each letter preserving its individual aspect ratio and bounds
        letterSprites.forEach((sprite, index) => {
          const validHeight = (sprite.height && !isNaN(sprite.height) && sprite.height > 0) ? sprite.height : 50;
          const validWidth = (sprite.width && !isNaN(sprite.width) && sprite.width > 0) ? sprite.width : 50;
          const scaledHeight = Math.min(maxLetterHeight, validHeight);
          const scaledWidth = (validWidth / validHeight) * scaledHeight;
          console.log(`📐 text-top letter ${index}: ${sprite.width}->${validWidth}, ${sprite.height}->${validHeight}, final: ${scaledWidth}x${scaledHeight} at (${currentX},${workspaceHeight * 0.12})`);
          
          result.push({
            ...sprite,
            x: currentX,
            y: workspaceHeight * 0.12,
            width: scaledWidth,  // Preserve aspect ratio
            height: scaledHeight // Preserve bounds
          });
          
          currentX += scaledWidth + letterSpacing;
        });
      }
      
      return result;
    }
  },
  {
    id: 'text-bottom',
    name: 'Text on Bottom',
    description: 'Large symbol above, text below',
    icon: '🔽',
    applyLayout: (sprites, workspaceWidth, workspaceHeight) => {
      const symbolSprites = sprites.filter(s => s.type === 'symbol' || s.type === 'element');
      const letterSprites = sprites.filter(s => s.type === 'letter');
      
      const result = [];
      
      // Position main symbol in upper area preserving original bounds and aspect ratio
      symbolSprites.forEach((sprite, index) => {
        // PRESERVE ORIGINAL ASPECT RATIO AND BOUNDS instead of forcing square
        const maxSize = Math.min(workspaceWidth * 0.5, workspaceHeight * 0.45);
        
        // Validate sprite dimensions to prevent NaN calculations
        const validWidth = (sprite.width && !isNaN(sprite.width) && sprite.width > 0) ? sprite.width : 100;
        const validHeight = (sprite.height && !isNaN(sprite.height) && sprite.height > 0) ? sprite.height : 100;
        const aspectRatio = validWidth / validHeight;
        
        console.log(`📐 text-bottom sprite ${index}: width=${sprite.width} -> ${validWidth}, height=${sprite.height} -> ${validHeight}, ratio=${aspectRatio}`);
        
        let newWidth, newHeight;
        if (aspectRatio > 1) {
          // Wider than tall - limit by width
          newWidth = Math.min(maxSize, validWidth);
          newHeight = newWidth / aspectRatio;
        } else {
          // Taller than wide - limit by height  
          newHeight = Math.min(maxSize, validHeight);
          newWidth = newHeight * aspectRatio;
        }
        
        // DISTINCT POSITIONING: Arrange symbols in a horizontal line in upper area
        const symbolSpacing = Math.max(20, workspaceWidth * 0.02);
        const totalSymbolsWidth = (symbolSprites.length * newWidth) + ((symbolSprites.length - 1) * symbolSpacing);
        const startX = (workspaceWidth - totalSymbolsWidth) / 2;
        
        result.push({
          ...sprite,
          x: startX + (index * (newWidth + symbolSpacing)),
          y: workspaceHeight * 0.2, // Position at 20% height
          width: newWidth,
          height: newHeight
        });
      });
      
      // Position text letters closer to symbol at bottom preserving bounds
      if (letterSprites.length > 0) {
        // PRESERVE ORIGINAL LETTER BOUNDS instead of forcing uniform sizing
        const maxLetterHeight = workspaceHeight * 0.15;
        const letterSpacing = 20;
        
        // Calculate total width needed preserving individual letter sizes
        const totalLetterWidth = letterSprites.reduce((total, sprite) => {
          const validHeight = (sprite.height && !isNaN(sprite.height) && sprite.height > 0) ? sprite.height : 50;
          const validWidth = (sprite.width && !isNaN(sprite.width) && sprite.width > 0) ? sprite.width : 50;
          const scaledHeight = Math.min(maxLetterHeight, validHeight);
          const scaledWidth = (validWidth / validHeight) * scaledHeight;
          console.log(`📐 text-bottom letter total calc: ${sprite.width}->${validWidth}, ${sprite.height}->${validHeight}, scaled: ${scaledWidth}x${scaledHeight}`);
          return total + scaledWidth;
        }, 0);
        const totalSpacingWidth = (letterSprites.length - 1) * letterSpacing;
        const totalTextWidth = totalLetterWidth + totalSpacingWidth;
        
        let currentX = (workspaceWidth - totalTextWidth) / 2;
        
        // Position each letter preserving its individual aspect ratio and bounds
        letterSprites.forEach((sprite, index) => {
          const validHeight = (sprite.height && !isNaN(sprite.height) && sprite.height > 0) ? sprite.height : 50;
          const validWidth = (sprite.width && !isNaN(sprite.width) && sprite.width > 0) ? sprite.width : 50;
          const scaledHeight = Math.min(maxLetterHeight, validHeight);
          const scaledWidth = (validWidth / validHeight) * scaledHeight;
          console.log(`📐 text-bottom letter ${index}: ${sprite.width}->${validWidth}, ${sprite.height}->${validHeight}, final: ${scaledWidth}x${scaledHeight} at (${currentX},${workspaceHeight * 0.75})`);
          
          result.push({
            ...sprite,
            x: currentX,
            y: workspaceHeight * 0.75,
            width: scaledWidth,  // Preserve aspect ratio
            height: scaledHeight // Preserve bounds
          });
          
          currentX += scaledWidth + letterSpacing;
        });
      }
      
      return result;
    }
  },
  {
    id: 'text-overlay',
    name: 'Text Overlay',
    description: 'Text centered over symbol',
    icon: '🎯',
    applyLayout: (sprites, workspaceWidth, workspaceHeight) => {
      const symbolSprites = sprites.filter(s => s.type === 'symbol' || s.type === 'element');
      const letterSprites = sprites.filter(s => s.type === 'letter');
      
      const result = [];
      
      // Position main symbol centered preserving original bounds and aspect ratio
      symbolSprites.forEach((sprite, index) => {
        // PRESERVE ORIGINAL ASPECT RATIO AND BOUNDS instead of forcing square
        const maxSize = Math.min(workspaceWidth * 0.4, workspaceHeight * 0.4);
        
        // Validate sprite dimensions to prevent NaN calculations
        const validWidth = (sprite.width && !isNaN(sprite.width) && sprite.width > 0) ? sprite.width : 100;
        const validHeight = (sprite.height && !isNaN(sprite.height) && sprite.height > 0) ? sprite.height : 100;
        const aspectRatio = validWidth / validHeight;
        
        console.log(`📐 text-overlay sprite ${index}: width=${sprite.width} -> ${validWidth}, height=${sprite.height} -> ${validHeight}, ratio=${aspectRatio}`);
        
        let newWidth, newHeight;
        if (aspectRatio > 1) {
          // Wider than tall - limit by width
          newWidth = Math.min(maxSize, validWidth);
          newHeight = newWidth / aspectRatio;
        } else {
          // Taller than wide - limit by height  
          newHeight = Math.min(maxSize, validHeight);
          newWidth = newHeight * aspectRatio;
        }
        
        // DISTINCT POSITIONING: Arrange symbols in a diagonal pattern for overlay effect
        const diagonalOffset = index * 40; // Stagger each symbol by 40px diagonally
        
        result.push({
          ...sprite,
          x: (workspaceWidth - newWidth) / 2 + diagonalOffset,
          y: (workspaceHeight - newHeight) / 2 + diagonalOffset,
          width: newWidth,
          height: newHeight
        });
      });
      
      // Position text letters with larger spacing around symbol preserving bounds
      if (letterSprites.length > 0) {
        // PRESERVE ORIGINAL LETTER BOUNDS instead of forcing uniform sizing
        const maxLetterHeight = workspaceHeight * 0.15;
        const letterSpacing = 20;
        
        // Calculate total width needed preserving individual letter sizes
        const totalLetterWidth = letterSprites.reduce((total, sprite) => {
          const validHeight = (sprite.height && !isNaN(sprite.height) && sprite.height > 0) ? sprite.height : 50;
          const validWidth = (sprite.width && !isNaN(sprite.width) && sprite.width > 0) ? sprite.width : 50;
          const scaledHeight = Math.min(maxLetterHeight, validHeight);
          const scaledWidth = (validWidth / validHeight) * scaledHeight;
          console.log(`📐 text-overlay letter total calc: ${sprite.width}->${validWidth}, ${sprite.height}->${validHeight}, scaled: ${scaledWidth}x${scaledHeight}`);
          return total + scaledWidth;
        }, 0);
        const totalSpacingWidth = (letterSprites.length - 1) * letterSpacing;
        const totalTextWidth = totalLetterWidth + totalSpacingWidth;
        
        let currentX = (workspaceWidth - totalTextWidth) / 2;
        
        // Position each letter preserving its individual aspect ratio and bounds
        letterSprites.forEach((sprite, index) => {
          const validHeight = (sprite.height && !isNaN(sprite.height) && sprite.height > 0) ? sprite.height : 50;
          const validWidth = (sprite.width && !isNaN(sprite.width) && sprite.width > 0) ? sprite.width : 50;
          const scaledHeight = Math.min(maxLetterHeight, validHeight);
          const scaledWidth = (validWidth / validHeight) * scaledHeight;
          console.log(`📐 text-overlay letter ${index}: ${sprite.width}->${validWidth}, ${sprite.height}->${validHeight}, final: ${scaledWidth}x${scaledHeight} at (${currentX},${workspaceHeight * 0.7})`);
          
          result.push({
            ...sprite,
            x: currentX,
            y: (workspaceHeight - scaledHeight) / 2,
            width: scaledWidth,  // Preserve aspect ratio
            height: scaledHeight // Preserve bounds
          });
          
          currentX += scaledWidth + letterSpacing;
        });
      }
      
      return result;
    }
  }
];

// Animation Template Configurations
export const ANIMATION_TEMPLATES: AnimationTemplate[] = [
  {
    id: 'bounce',
    name: 'Bounce',
    description: 'Symbol bounces up and down',
    icon: '🏀',
    duration: 1.5,
    applyAnimation: (sprites) => {
      sprites.forEach(sprite => {
        if (sprite.type === 'element' || sprite.type === 'symbol') {
          gsap.to(sprite, {
            y: sprite.y - 20,
            duration: 0.4,
            ease: "power2.out",
            repeat: -1,
            yoyo: true
          });
        }
      });
    }
  },
  {
    id: 'pulse',
    name: 'Pulse',
    description: 'Symbol scales in and out rhythmically',
    icon: '💓',
    duration: 1.2,
    applyAnimation: (sprites) => {
      sprites.forEach(sprite => {
        const originalWidth = sprite.width;
        const originalHeight = sprite.height;
        gsap.to(sprite, {
          width: originalWidth * 1.2,
          height: originalHeight * 1.2,
          x: sprite.x - originalWidth * 0.1,
          y: sprite.y - originalHeight * 0.1,
          duration: 0.6,
          ease: "power2.inOut",
          repeat: -1,
          yoyo: true
        });
      });
    }
  },
  {
    id: 'glow',
    name: 'Glow',
    description: 'Symbol glows with soft light',
    icon: '✨',
    duration: 2.0,
    applyAnimation: (sprites) => {
      // Glow effect would need custom canvas rendering
      console.log('🌟 Glow animation applied');
    }
  },
  {
    id: 'rotate',
    name: 'Rotate',
    description: 'Symbol rotates smoothly',
    icon: '🔄',
    duration: 3.0,
    applyAnimation: (sprites) => {
      sprites.forEach(sprite => {
        if (sprite.type === 'element' || sprite.type === 'symbol') {
          // Note: Canvas rotation would need transform origin setup
          console.log('🔄 Rotate animation applied to', sprite.id);
        }
      });
    }
  },
  {
    id: 'shake',
    name: 'Shake',
    description: 'Symbol shakes for excitement',
    icon: '🔥',
    duration: 0.8,
    applyAnimation: (sprites) => {
      sprites.forEach(sprite => {
        if (sprite.type === 'element' || sprite.type === 'symbol') {
          const originalX = sprite.x;
          gsap.to(sprite, {
            x: originalX + 5,
            duration: 0.1,
            ease: "power2.inOut",
            repeat: 7,
            yoyo: true,
            onComplete: () => {
              sprite.x = originalX;
            }
          });
        }
      });
    }
  },
  {
    id: 'sparkle',
    name: 'Sparkle',
    description: 'Text letters animate individually',
    icon: '🌟',
    duration: 2.0,
    applyAnimation: (sprites) => {
      const letterSprites = sprites.filter(s => s.type === 'letter');
      letterSprites.forEach((sprite, index) => {
        gsap.to(sprite, {
          y: sprite.y - 15,
          duration: 0.3,
          ease: "back.out(1.7)",
          delay: index * 0.1,
          repeat: -1,
          repeatDelay: 1.5,
          yoyo: true
        });
      });
    }
  },
  {
    id: 'flash',
    name: 'Flash',
    description: 'Quick flash animation',
    icon: '⚡',
    duration: 0.5,
    applyAnimation: (sprites) => {
      sprites.forEach(sprite => {
        // Flash effect with opacity
        gsap.to(sprite, {
          opacity: 0.3,
          duration: 0.1,
          repeat: 3,
          yoyo: true,
          onComplete: () => {
            sprite.opacity = 1;
          }
        });
      });
    }
  },
  {
    id: 'wave',
    name: 'Wave',
    description: 'Text letters wave in sequence',
    icon: '🌊',
    duration: 2.5,
    applyAnimation: (sprites) => {
      const letterSprites = sprites.filter(s => s.type === 'letter');
      letterSprites.forEach((sprite, index) => {
        gsap.to(sprite, {
          y: sprite.y - 10,
          duration: 0.4,
          ease: "sine.inOut",
          delay: index * 0.2,
          repeat: -1,
          repeatDelay: letterSprites.length * 0.2,
          yoyo: true
        });
      });
    }
  }
];
