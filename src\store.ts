import { create } from 'zustand';
import { GameConfig, AnimationWorkspace, DeviceProfile, MaskPreviewMode } from './types';
import { CONFIG_DEFAULTS } from './utils/configDefaults';

interface GameStore {
  // UI State
  currentStep: number;
  totalSteps: number;
  viewMode: 'simple' | 'advanced';
  isSpinning: boolean;
  gameType: string | null;
  currentQuestion: number;
  answers: Record<string, any>;
  isMobileView: boolean;
  showPreview: boolean;
  hasUnsavedChanges: boolean;
  lastSaved: Date | null;
  
  // Animation State
  animationTrigger: {
    type: 'small-win' | 'big-win' | 'mega-win' | 'freespins' | null;
    timestamp: number;
    isPlaying: boolean;
  };
  generatedAnimations: any | null;
  
  // Game Configuration
  config: Partial<GameConfig>;
  savedProgress: Record<string, any>;
  
  // Animation Studio State
  animationWorkspace: AnimationWorkspace;
  
  // Enhanced Animation Features
  animationProfiles: Array<{
    id: string;
    name: string;
    settings: Record<string, any>;
    isFavorite?: boolean;
    createdAt: Date;
  }>;
  aiSuggestionsEnabled: boolean;
  performanceMode: 'auto' | 'manual';
  usabilityTracking: boolean;
  
  // Navigation actions
  setStep: (step: number) => void;
  nextStep: () => void;
  prevStep: () => void;
  
  // UI state actions
  setViewMode: (mode: 'simple' | 'advanced') => void;
  setIsSpinning: (spinning: boolean) => void;
  setIsMobileView: (isMobile: boolean) => void;
  togglePreview: () => void;
  setUseVisualJourney: (useVisual: boolean) => void;
  
  // Game configuration actions
  setGameType: (type: string | null) => void;
  setAnswer: (questionId: string, answer: any) => void;
  updateConfig: (update: Partial<GameConfig>) => void;
  saveProgress: () => void;
  resetConfig: () => void;
  
  // Grid layout actions
  setGridOrientation: (orientation: 'landscape' | 'portrait') => void;
  
  // Animation actions
  triggerAnimation: (type: 'small-win' | 'big-win' | 'mega-win' | 'freespins') => void;
  setGeneratedAnimations: (animations: any) => void;
  clearAnimationTrigger: () => void;
  
  // Animation Studio actions
  setAnimationPreset: (preset: string) => void;
  setMaskEditMode: (enabled: boolean) => void;
  setMaskPreviewMode: (mode: MaskPreviewMode) => void;
  setPerformanceMode: (mode: DeviceProfile) => void;
  toggleEasingCurve: () => void;
  
  // Enhanced Animation actions
  addAnimationProfile: (profile: { id: string; name: string; settings: Record<string, any> }) => void;
  removeAnimationProfile: (profileId: string) => void;
  toggleProfileFavorite: (profileId: string) => void;
  setAISuggestionsEnabled: (enabled: boolean) => void;
  setUsabilityTracking: (enabled: boolean) => void;
}

// Ensure we always have a default game type selection
const initialConfig = {
  ...CONFIG_DEFAULTS,
  selectedGameType: 'classic-reels', // Force Classic Reels as default selection
  reels: {
    ...CONFIG_DEFAULTS.reels,
    layout: {
      ...CONFIG_DEFAULTS.reels?.layout,
      reels: 5,
      rows: 3,
      orientation: 'landscape'
    }
  }
};

// Create the store
export const useGameStore = create<GameStore>((set, get) => ({
  // State
  currentStep: 0,
  totalSteps: 16, // Default to 16 steps for slots game type
  viewMode: 'simple',
  isSpinning: false,
  gameType: 'slots', // Default to slots game type
  currentQuestion: 0,
  answers: {},
  config: initialConfig,
  savedProgress: {},
  isMobileView: typeof window !== 'undefined' ? window.innerWidth < 768 : false,
  showPreview: false,
  hasUnsavedChanges: false,
  lastSaved: null,
  
  // Animation state
  animationTrigger: {
    type: null,
    timestamp: 0,
    isPlaying: false,
  },
  generatedAnimations: null,
  
  // Animation Studio state
  animationWorkspace: {
    selectedPreset: 'modern',
    maskEditMode: false,
    previewMode: 'normal',
    performanceMode: 'desktop',
    showEasingCurve: false
  },
  
  // Enhanced Animation Features state
  animationProfiles: [],
  aiSuggestionsEnabled: true,
  performanceMode: 'auto',
  usabilityTracking: true,

  // Navigation actions
  setStep: (step) => set((state) => {
    // Direct setStep called
    
    // Safety check - ensure step is within valid range
    if (step < 0) step = 0;
    if (step >= state.totalSteps) step = state.totalSteps - 1;
    
    // Special handling for critical Step 0 -> Step 1 transition
    if (state.currentStep === 0 && step === 1) {
      // Critical step transition: 0 -> 1
      
      // Ensure we preserve the selected theme and game type
      const preservedTheme = state.config?.theme?.selectedThemeId;
      const preservedGameType = state.config.selectedGameType || 'classic-reels';
      
      // Preserving selections:
      // theme: preservedTheme,
      // gameType: preservedGameType
      
      // Extra verification - Force classic-reels as fallback
      if (!state.config.selectedGameType) {
        // No game type selected, forcing classic-reels
        // Update will be merged below
      }
    }
    
    // Save current step progress before changing - with more complete data
    const updatedSavedProgress = {
      ...state.savedProgress,
      [state.currentStep]: {
        config: state.config,
        answers: state.answers,
        timestamp: new Date(),
        savedDetails: {
          stepName: state.currentStep === 0 ? 'Theme Selection' : 
                   state.currentStep === 1 ? 'Game Type' : `Step ${state.currentStep + 1}`,
          selectedGameType: state.config.selectedGameType,
          selectedTheme: state.config?.theme?.selectedThemeId
        }
      }
    };
    
    // Log step change for debugging
    // Navigating between steps
    
    // Scroll to top with animation - outside setTimeout to ensure it happens synchronously
    window.scrollTo({ top: 0, behavior: 'smooth' });
    
    // Add haptic feedback for mobile if supported
    if ('vibrate' in navigator) {
      try {
        navigator.vibrate(50);
      } catch (e) {
        // Ignore vibration errors
      }
    }
    
    return { 
      currentStep: step,
      savedProgress: updatedSavedProgress,
      hasUnsavedChanges: true
    };
  }),
  
  nextStep: () => {
    const { currentStep, totalSteps, setStep, config } = get();
    // nextStep() called
    
    if (currentStep < totalSteps - 1) {
      // Calculate target step
      const targetStep = currentStep + 1;
      // Moving to next step
      
      // Special handling for Step 0 -> Step 1 transition
      if (currentStep === 0) {
        // Critical step 0 -> 1 transition, using synchronous update
        
        // Save current state to localStorage as backup
        try {
          localStorage.setItem('slotai_navigation_backup', JSON.stringify({
            timestamp: Date.now(),
            targetStep: 1,
            themeId: config?.theme?.selectedThemeId,
            gameId: config?.gameId,
            selectedGameType: config.selectedGameType || 'classic-reels'
          }));
        } catch (e) {
          // Ignore localStorage errors
        }
        
        // Directly update state - no timeouts to avoid race conditions
        setStep(targetStep);
        
        // Add verification check
        const verifyInterval = setInterval(() => {
          const currentState = get();
          if (currentState.currentStep === targetStep) {
            // Navigation verified successful
            clearInterval(verifyInterval);
          }
        }, 100);
        
        // Clear interval after maximum time
        setTimeout(() => clearInterval(verifyInterval), 2000);
      } else {
        // For non-critical transitions, use standard approach
        setStep(targetStep);
      }
    } else {
      console.warn('🔄 Already at last step, cannot proceed further');
    }
  },
  
  prevStep: () => {
    const { currentStep, setStep } = get();
    // prevStep() called
    
    if (currentStep > 0) {
      const targetStep = currentStep - 1;
      // Moving back to previous step
      
      // Directly update state - no timeouts for more reliable navigation
      setStep(targetStep);
    } else {
      console.warn('🔄 Already at first step, cannot go back');
    }
  },
  
  // UI state actions
  setViewMode: (mode) => set({ viewMode: mode }),
  setIsSpinning: (spinning) => set({ isSpinning: spinning }),
  setIsMobileView: (isMobile) => set({ isMobileView: isMobile }),
  togglePreview: () => set((state) => ({ showPreview: !state.showPreview })),
  
  // Game configuration actions
  setGameType: (type) => {
    set({ gameType: type });
    // Always reset config when switching game types
    if (type) {
      // Set total steps based on game type
      const totalSteps = type === 'slots' ? 16 : 
                         type === 'scratch' ? 6 : 16;
      
      set({ 
        currentStep: 0, 
        currentQuestion: 0, 
        answers: {}, 
        config: initialConfig,
        savedProgress: {},
        hasUnsavedChanges: false,
        lastSaved: null,
        totalSteps 
      });
    }
  },
  
  setAnswer: (questionId, answer) => set((state) => {
    const newAnswers = { ...state.answers, [questionId]: answer };
    let configUpdate = {};
    
    if (questionId === 'payMechanism') {
      configUpdate = {
        reels: {
          ...state.config.reels,
          payMechanism: answer,
          ...(answer === 'cluster' ? {
            cluster: {
              minSymbols: 5,
              diagonalAllowed: false,
              payouts: {
                5: 5,
                8: 20,
                12: 100
              }
            }
          } : {})
        }
      };
    } else if (questionId === 'gridSize') {
      const gridConfig = answer.config || { reels: 5, rows: 3 };
      configUpdate = {
        reels: {
          ...state.config.reels,
          layout: {
            ...state.config.reels?.layout,
            reels: gridConfig.reels,
            rows: gridConfig.rows
          }
        }
      };
    }

    return {
      answers: newAnswers,
      currentQuestion: state.currentQuestion < 2 ? state.currentQuestion + 1 : state.currentQuestion,
      config: {
        ...state.config,
        ...configUpdate
      },
      hasUnsavedChanges: true
    };
  }),
  
  updateConfig: (update) => set((state) => {
    // Preserve gameType selection when updating config
    const preservedGameType = update.selectedGameType || state.config.selectedGameType;
    
    // Create updated config with selection preserved
    const updatedConfig = { 
      ...state.config, 
      ...update, 
      // Always keep the selected game type if it exists
      selectedGameType: preservedGameType
    };
    
    // Store updateConfig - preserved gameType
    
    return { 
      config: updatedConfig,
      hasUnsavedChanges: true
    };
  }),
  
  saveProgress: () => set((state) => {
    try {
      const gameId = state.config.gameId || `game_${Date.now()}`;
      
      // Create a version without the large image data to avoid quota issues
      const minimalConfig = { ...state.config };
      
      // Remove ALL large image data from config to avoid quota issues
      if (minimalConfig.theme?.generated) {
        minimalConfig.theme = {
          ...minimalConfig.theme,
          generated: {
            ...minimalConfig.theme.generated,
            // Replace with minimal indicators instead of large base64 strings
            symbols: minimalConfig.theme.generated.symbols ? 
              minimalConfig.theme.generated.symbols.map(() => 'symbol-exists') : [],
            background: minimalConfig.theme.generated.background ? 'background-exists' : '',
            logo: minimalConfig.theme.generated.logo ? 'logo-exists' : ''
          }
        };
      }
      
      // Also clean up any step data with large images
      const cleanAnswers = { ...state.answers };
      Object.keys(cleanAnswers).forEach(stepKey => {
        const stepData = cleanAnswers[stepKey];
        if (stepData && typeof stepData === 'object') {
          // Remove any base64 image data from answers
          Object.keys(stepData).forEach(key => {
            const value = stepData[key];
            if (typeof value === 'string' && value.startsWith('data:image/')) {
              stepData[key] = 'image-data-removed';
            }
          });
        }
      });
      
      const saveData = {
        gameType: state.gameType,
        config: minimalConfig, // Use minimal config
        currentStep: state.currentStep,
        answers: cleanAnswers, // Use cleaned answers
        savedAt: new Date().toISOString()
      };
      
      // Clear old saves before saving new one to prevent quota issues
      try {
        // Get all localStorage keys
        const keys = Object.keys(localStorage);
        const slotaiKeys = keys.filter(key => key.startsWith('slotai_save_'));
        
        // If we have more than 5 saves, remove the oldest ones
        if (slotaiKeys.length > 5) {
          // Sort by timestamp (gameId includes timestamp)
          slotaiKeys.sort();
          // Remove all but the 4 most recent
          const keysToRemove = slotaiKeys.slice(0, slotaiKeys.length - 4);
          keysToRemove.forEach(key => localStorage.removeItem(key));
          console.log(`🧹 Cleared ${keysToRemove.length} old saves to free up space`);
        }
        
        // Create minimal save data without large base64 images
        const minimalSaveData = {
          ...saveData,
          generatedSymbols: saveData.generatedSymbols?.map(symbol => ({
            ...symbol,
            // Remove base64 data, keep only metadata
            url: symbol.url?.length > 100 ? '[base64-data-removed]' : symbol.url,
            originalUrl: symbol.originalUrl?.length > 100 ? '[base64-data-removed]' : symbol.originalUrl
          })) || [],
          gameAssets: {
            ...saveData.gameAssets,
            // Remove large background and frame data
            background: saveData.gameAssets?.background?.length > 100 ? '[base64-data-removed]' : saveData.gameAssets?.background,
            frame: saveData.gameAssets?.frame?.length > 100 ? '[base64-data-removed]' : saveData.gameAssets?.frame
          }
        };
        
        // Save minimal data to localStorage
        localStorage.setItem(`slotai_save_${gameId}`, JSON.stringify(minimalSaveData));
        console.log('✅ Game progress saved successfully');
      } catch (quotaError) {
        console.warn('📦 Storage quota exceeded, trying gradual cleanup...');
        
        // First try: remove just old saves (more than 2)
        const slotaiKeys = Object.keys(localStorage)
          .filter(key => key.startsWith('slotai_save_'))
          .sort();
        
        if (slotaiKeys.length > 2) {
          const keysToRemove = slotaiKeys.slice(0, slotaiKeys.length - 2);
          keysToRemove.forEach(key => localStorage.removeItem(key));
          console.log(`🧹 Gradual cleanup: removed ${keysToRemove.length} old saves`);
          
          try {
            localStorage.setItem(`slotai_save_${gameId}`, JSON.stringify(saveData));
            console.log('✅ Game progress saved after gradual cleanup');
            return;
          } catch (retryError) {
            console.warn('⚠️ Gradual cleanup insufficient, trying aggressive cleanup...');
          }
        }
        
        // Last resort: clear all slotai saves if quota still exceeded
        slotaiKeys.forEach(key => localStorage.removeItem(key));
        console.warn('🧹 Aggressive cleanup: cleared all saves');
        
        // Try one more time
        try {
          localStorage.setItem(`slotai_save_${gameId}`, JSON.stringify(saveData));
          console.log('✅ Game progress saved after aggressive cleanup');
        } catch (finalError) {
          console.error('❌ Failed to save even after clearing all data:', finalError);
          // Don't throw - just continue without saving to prevent crashes
          console.warn('⚠️ Continuing without saving to localStorage');
        }
      }
      
      return {
        hasUnsavedChanges: false,
        lastSaved: new Date(),
        savedProgress: {
          ...state.savedProgress,
          [state.currentStep]: {
            config: state.config, // Keep full config in memory
            answers: state.answers,
            timestamp: new Date()
          }
        }
      };
    } catch (e) {
      console.error('Failed to save progress:', e);
      return state;
    }
  }),
  
  resetConfig: () => set({ 
    // Preserve the "classic-reels" selection even when resetting
    config: { 
      ...initialConfig
    }, 
    currentStep: 0, 
    gameType: 'slots',
    currentQuestion: 0,
    answers: {},
    savedProgress: {},
    hasUnsavedChanges: false,
    lastSaved: null
  }),
  
  // New action for setting grid orientation that preserves grid dimensions
  setGridOrientation: (orientation) => set((state) => {
    // Get current reels and rows
    const currentReels = state.config.reels?.layout?.reels || 5;
    const currentRows = state.config.reels?.layout?.rows || 3;
    
    // Update config with new orientation while preserving grid dimensions
    return {
      config: {
        ...state.config,
        reels: {
          ...state.config.reels,
          layout: {
            ...state.config.reels?.layout,
            reels: currentReels,
            rows: currentRows,
            orientation
          }
        }
      }
    };
  }),
  
  // Animation actions
  triggerAnimation: (type) => set((state) => ({
    animationTrigger: {
      type,
      timestamp: Date.now(),
      isPlaying: true,
    }
  })),
  
  setGeneratedAnimations: (animations) => set({ generatedAnimations: animations }),
  
  clearAnimationTrigger: () => set((state) => ({
    animationTrigger: {
      ...state.animationTrigger,
      type: null,
      isPlaying: false,
    }
  })),

  // Animation Studio actions
  setAnimationPreset: (preset) => set((state) => ({
    animationWorkspace: {
      ...state.animationWorkspace,
      selectedPreset: preset
    }
  })),

  setMaskEditMode: (enabled) => set((state) => ({
    animationWorkspace: {
      ...state.animationWorkspace,
      maskEditMode: enabled
    }
  })),

  setMaskPreviewMode: (mode) => set((state) => ({
    animationWorkspace: {
      ...state.animationWorkspace,
      previewMode: mode
    }
  })),

  setPerformanceMode: (mode) => set((state) => ({
    animationWorkspace: {
      ...state.animationWorkspace,
      performanceMode: mode
    }
  })),

  toggleEasingCurve: () => set((state) => ({
    animationWorkspace: {
      ...state.animationWorkspace,
      showEasingCurve: !state.animationWorkspace.showEasingCurve
    }
  })),

  // Enhanced Animation actions
  addAnimationProfile: (profile) => set((state) => ({
    animationProfiles: [...state.animationProfiles, { ...profile, createdAt: new Date() }]
  })),

  removeAnimationProfile: (profileId) => set((state) => ({
    animationProfiles: state.animationProfiles.filter(p => p.id !== profileId)
  })),

  toggleProfileFavorite: (profileId) => set((state) => ({
    animationProfiles: state.animationProfiles.map(p => 
      p.id === profileId ? { ...p, isFavorite: !p.isFavorite } : p
    )
  })),

  setAISuggestionsEnabled: (enabled) => set({ aiSuggestionsEnabled: enabled }),

  setUsabilityTracking: (enabled) => set({ usabilityTracking: enabled })
}));

// Expose store to window for global access (helps with emergency navigation and home button)
if (typeof window !== 'undefined') {
  window.useGameStore = useGameStore;
}