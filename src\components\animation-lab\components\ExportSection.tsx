/**
 * Export Section Component
 * Export options and download functionality for Simple Mode
 */

import React, { useState } from 'react';
import { simpleAnimationEngine, type SpriteAnimation } from '../../../utils/simpleAnimationEngine';

interface ExportSectionProps {
  atlasResult: any;
  disabled: boolean;
  animations?: SpriteAnimation[];
}

interface ExportFormat {
  id: string;
  name: string;
  icon: string;
  description: string;
  fileType: string;
  color: string;
}

const exportFormats: ExportFormat[] = [
  {
    id: 'json',
    name: 'JSON Atlas',
    icon: '📋',
    description: 'TexturePacker compatible atlas data',
    fileType: '.json',
    color: '#3b82f6'
  },
  {
    id: 'css',
    name: 'CSS Sprites',
    icon: '🎨',
    description: 'CSS animations for web games',
    fileType: '.css',
    color: '#10b981'
  },
  {
    id: 'gsap',
    name: 'GSAP Code',
    icon: '⚡',
    description: 'GSAP timeline animations',
    fileType: '.js',
    color: '#f59e0b'
  },
  {
    id: 'unity',
    name: 'Unity Package',
    icon: '🎮',
    description: 'Unity-ready animation package',
    fileType: '.unitypackage',
    color: '#8b5cf6'
  }
];

const ExportSection: React.FC<ExportSectionProps> = ({ atlasResult, disabled, animations = [] }) => {
  const [selectedFormat, setSelectedFormat] = useState<string>('json');
  const [isExporting, setIsExporting] = useState(false);
  const [exportSuccess, setExportSuccess] = useState<string | null>(null);

  const handleExport = async (formatId: string) => {
    if (disabled || !atlasResult) return;

    setIsExporting(true);
    setExportSuccess(null);

    try {
      // Simulate export processing
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      const format = exportFormats.find(f => f.id === formatId);
      if (!format) throw new Error('Invalid format');

      // Create mock download
      const filename = `animated_sprites_${Date.now()}${format.fileType}`;
      
      // Generate export data based on format
      let exportData = '';
      switch (formatId) {
        case 'json':
          exportData = JSON.stringify({
            meta: {
              app: 'SlotAI Animation Lab',
              version: '2.0',
              format: 'RGBA8888',
              size: { w: 400, h: 300 }
            },
            frames: atlasResult.atlasMetadata.frames,
            animations: animations.reduce((acc, anim) => {
              acc[anim.spriteId] = {
                duration: anim.duration,
                loop: anim.loop,
                keyframes: anim.keyframes
              };
              return acc;
            }, {} as Record<string, any>)
          }, null, 2);
          break;
        case 'css':
          exportData = `/* Generated by SlotAI Animation Lab 2.0 */\n` +
            animations.map(anim => 
              `@keyframes ${anim.spriteId}-animation {\n` +
              anim.keyframes.map((kf, i) => 
                `  ${(kf.time * 100).toFixed(1)}% {\n` +
                Object.entries(kf.properties).map(([prop, val]) => 
                  `    ${prop}: ${val}${prop.includes('scale') || prop === 'alpha' ? '' : 'px'};\n`
                ).join('') +
                `  }`
              ).join('\n') +
              `\n}\n\n.${anim.spriteId} {\n  animation: ${anim.spriteId}-animation ${anim.duration}ms infinite;\n}`
            ).join('\n\n');
          break;
        case 'gsap':
          exportData = simpleAnimationEngine.generateGSAPCode(animations);
          break;
        case 'unity':
          exportData = `// Unity Animation Data - SlotAI Animation Lab 2.0\n` +
            `// Copy this into your Unity AnimationClip\n\n` +
            animations.map(anim => 
              `// ${anim.spriteId}\n` +
              `Duration: ${anim.duration / 1000}s\n` +
              `Loop: ${anim.loop}\n` +
              anim.keyframes.map(kf => 
                `Time: ${kf.time} - ${JSON.stringify(kf.properties)}`
              ).join('\n')
            ).join('\n\n');
          break;
      }

      // Create download
      const blob = new Blob([exportData], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      setExportSuccess(format.name);
      console.log(`📤 Exported as ${format.name}:`, filename);

    } catch (error) {
      console.error('❌ Export failed:', error);
      alert('Export failed. Please try again.');
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <div>
      {/* Header */}
      <h4 style={{
        margin: '0 0 16px 0',
        fontSize: '16px',
        fontWeight: '600',
        color: '#374151'
      }}>
        Export Options
      </h4>

      {/* Export formats */}
      <div style={{
        display: 'flex',
        flexDirection: 'column',
        gap: '8px',
        marginBottom: '16px'
      }}>
        {exportFormats.map((format) => (
          <button
            key={format.id}
            onClick={() => setSelectedFormat(format.id)}
            disabled={disabled}
            style={{
              padding: '12px',
              borderRadius: '8px',
              border: selectedFormat === format.id ? `2px solid ${format.color}` : '1px solid #e5e7eb',
              background: disabled ? '#f9fafb' : 
                         selectedFormat === format.id ? `${format.color}10` : 'white',
              cursor: disabled ? 'not-allowed' : 'pointer',
              textAlign: 'left',
              transition: 'all 0.2s ease',
              opacity: disabled ? 0.5 : 1
            }}
          >
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              marginBottom: '4px'
            }}>
              <span style={{ fontSize: '16px' }}>{format.icon}</span>
              <span style={{
                fontSize: '14px',
                fontWeight: '600',
                color: selectedFormat === format.id ? format.color : '#374151'
              }}>
                {format.name}
              </span>
              <span style={{
                fontSize: '11px',
                color: '#6b7280',
                background: '#f3f4f6',
                padding: '2px 6px',
                borderRadius: '4px'
              }}>
                {format.fileType}
              </span>
            </div>
            <div style={{
              fontSize: '12px',
              color: '#6b7280',
              lineHeight: '1.3'
            }}>
              {format.description}
            </div>
          </button>
        ))}
      </div>

      {/* Export button */}
      <button
        onClick={() => handleExport(selectedFormat)}
        disabled={disabled || isExporting}
        style={{
          width: '100%',
          padding: '12px 16px',
          borderRadius: '8px',
          border: 'none',
          background: disabled ? '#e5e7eb' : 
                     isExporting ? '#6b7280' : '#059669',
          color: 'white',
          fontSize: '14px',
          fontWeight: '600',
          cursor: disabled ? 'not-allowed' : 'pointer',
          transition: 'all 0.2s ease',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          gap: '8px'
        }}
      >
        {isExporting ? (
          <>
            <div style={{
              width: '14px',
              height: '14px',
              border: '2px solid white',
              borderTop: '2px solid transparent',
              borderRadius: '50%',
              animation: 'spin 1s linear infinite'
            }} />
            Exporting...
          </>
        ) : (
          <>
            📥 Download {exportFormats.find(f => f.id === selectedFormat)?.name}
          </>
        )}
      </button>

      {/* Success message */}
      {exportSuccess && (
        <div style={{
          marginTop: '12px',
          padding: '10px',
          background: '#f0fdf4',
          border: '1px solid #bbf7d0',
          borderRadius: '6px',
          fontSize: '12px',
          color: '#059669',
          textAlign: 'center'
        }}>
          ✅ Successfully exported as {exportSuccess}
        </div>
      )}

      {/* Export info */}
      {!disabled && atlasResult && (
        <div style={{
          marginTop: '16px',
          padding: '12px',
          background: '#f8fafc',
          border: '1px solid #e2e8f0',
          borderRadius: '6px'
        }}>
          <div style={{
            fontSize: '12px',
            fontWeight: '600',
            color: '#475569',
            marginBottom: '6px'
          }}>
            📊 Export Summary
          </div>
          <div style={{
            fontSize: '11px',
            color: '#64748b',
            lineHeight: '1.4'
          }}>
            • {Object.keys(atlasResult.atlasMetadata.frames).length} sprites detected<br/>
            • {animations.length} animations generated<br/>
            • Optimized for 60fps performance<br/>
            • Ready for game engine integration
          </div>
        </div>
      )}

      {/* Help text */}
      {disabled && (
        <div style={{
          marginTop: '12px',
          fontSize: '12px',
          color: '#9ca3af',
          textAlign: 'center',
          fontStyle: 'italic'
        }}>
          Apply animations first to enable export
        </div>
      )}

      <style>{`
        @keyframes spin {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
};

export default ExportSection;