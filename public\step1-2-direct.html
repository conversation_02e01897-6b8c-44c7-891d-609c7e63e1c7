<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Direct Game Type Selection</title>
  <style>
    body {
      font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background-color: #f5f5f5;
      color: #333;
      margin: 0;
      padding: 20px;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    .header {
      text-align: center;
      margin-bottom: 30px;
      padding-bottom: 20px;
      border-bottom: 1px solid #eee;
    }
    .title {
      color: #222;
      margin-bottom: 10px;
    }
    .subtitle {
      color: #666;
      font-weight: normal;
    }
    .game-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 20px;
      margin-bottom: 30px;
    }
    .game-card {
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      overflow: hidden;
      transition: all 0.3s ease;
      cursor: pointer;
    }
    .game-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    .game-card.selected {
      border-color: #2563eb;
      box-shadow: 0 0 0 2px #3b82f6;
    }
    .game-image {
      height: 180px;
      background-size: cover;
      background-position: center;
    }
    .game-content {
      padding: 15px;
    }
    .game-title {
      font-size: 18px;
      font-weight: bold;
      margin-bottom: 8px;
    }
    .game-description {
      color: #666;
      font-size: 14px;
      margin-bottom: 15px;
    }
    .button-container {
      display: flex;
      justify-content: center;
      margin-top: 30px;
    }
    .button {
      background-color: #3b82f6;
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 6px;
      font-size: 16px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;
    }
    .button:hover {
      background-color: #2563eb;
    }
    .button:disabled {
      background-color: #9ca3af;
      cursor: not-allowed;
    }
    .features {
      margin-top: 10px;
    }
    .feature {
      display: inline-block;
      background-color: #f3f4f6;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      margin-right: 5px;
      margin-bottom: 5px;
    }
    .alert {
      background-color: #fef2f2;
      color: #991b1b;
      padding: 12px;
      border-radius: 6px;
      margin-bottom: 20px;
      border-left: 4px solid #ef4444;
    }
    .info {
      background-color: #eff6ff;
      color: #1e40af;
      padding: 12px;
      border-radius: 6px;
      margin-bottom: 20px;
      border-left: 4px solid #3b82f6;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1 class="title">Direct Game Type Selection</h1>
      <h2 class="subtitle">Step 2: Choose a game type for your slot machine</h2>
      <div class="info">
        This page allows you to directly access Step 2 of the slot creation process.
        Your theme selection from Step 1 has been preserved.
      </div>
    </div>
    
    <div class="game-grid">
      <div class="game-card" data-type="classic-reels" onclick="selectGameType(this, 'classic-reels')">
        <div class="game-image" style="background-image: url('/themes/classic-reels.png');"></div>
        <div class="game-content">
          <div class="game-title">Classic Reels</div>
          <div class="game-description">5x3 grid with payline wins</div>
          <div class="features">
            <span class="feature">Multiple paylines</span>
            <span class="feature">Traditional symbols</span>
            <span class="feature">Familiar mechanics</span>
          </div>
        </div>
      </div>
      
      <div class="game-card" data-type="ways-slots" onclick="selectGameType(this, 'ways-slots')">
        <div class="game-image" style="background-image: url('/themes/ways-to-win.png');"></div>
        <div class="game-content">
          <div class="game-title">Ways to Win</div>
          <div class="game-description">Win in any position on adjacent reels</div>
          <div class="features">
            <span class="feature">243-1024 ways</span>
            <span class="feature">Adjacent reel wins</span>
            <span class="feature">Higher hit frequency</span>
          </div>
        </div>
      </div>
      
      <div class="game-card" data-type="grid-slots" onclick="selectGameType(this, 'grid-slots')">
        <div class="game-image" style="background-image: url('/themes/grid-slot.png');"></div>
        <div class="game-content">
          <div class="game-title">Grid Slots</div>
          <div class="game-description">Cluster pays on a large grid</div>
          <div class="features">
            <span class="feature">Cluster pays</span>
            <span class="feature">Symbol cascades</span>
            <span class="feature">Expanding grid</span>
          </div>
        </div>
      </div>
    </div>
    
    <div class="button-container">
      <button id="continueButton" class="button" disabled onclick="continueToNextStep()">Continue to Next Step</button>
    </div>
  </div>

  <script>
    let selectedType = null;
    
    // Function to select a game type
    function selectGameType(element, type) {
      // Remove selection from all cards
      document.querySelectorAll('.game-card').forEach(card => {
        card.classList.remove('selected');
      });
      
      // Add selection to clicked card
      element.classList.add('selected');
      
      // Enable continue button
      document.getElementById('continueButton').disabled = false;
      
      // Store selected type
      selectedType = type;
      
      // Show notification
      showNotification(`${element.querySelector('.game-title').textContent} selected!`);
      
      // Save to localStorage
      saveSelection(type);
    }
    
    // Function to save selection
    function saveSelection(type) {
      // Get any existing theme selection from Step 1
      let themeId = localStorage.getItem('slotai_theme_id') || 'default-theme';
      let gameId = localStorage.getItem('slotai_game_id') || `game_${Date.now()}`;
      
      // Save game data to localStorage
      const gameData = {
        selectedGameType: type,
        gameTypeInfo: {
          id: type,
          title: document.querySelector(`.game-card[data-type="${type}"] .game-title`).textContent,
          description: document.querySelector(`.game-card[data-type="${type}"] .game-description`).textContent,
          selectedAt: new Date().toISOString()
        },
        gameId: gameId,
        theme: {
          selectedThemeId: themeId
        }
      };
      
      localStorage.setItem('slotai_game_data', JSON.stringify(gameData));
      console.log('Game data saved:', gameData);
    }
    
    // Function to continue to the next step
    function continueToNextStep() {
      if (!selectedType) {
        alert('Please select a game type first');
        return;
      }
      
      // Set up emergency navigation data
      localStorage.setItem('slotai_emergency_nav', 'true');
      localStorage.setItem('slotai_target_step', '2'); // Go to step 2 (Step 3 in the UI which is 0-indexed)
      localStorage.setItem('slotai_timestamp', Date.now().toString());
      
      // Navigate to the main app with params to force the correct step
      window.location.href = '/?step=2&force=true&preserve_ui=true&t=' + Date.now();
    }
    
    // Function to show notification
    function showNotification(message) {
      // Check if notification container exists
      let container = document.getElementById('notification-container');
      if (!container) {
        container = document.createElement('div');
        container.id = 'notification-container';
        container.style.cssText = `
          position: fixed;
          bottom: 20px;
          right: 20px;
          z-index: 1000;
        `;
        document.body.appendChild(container);
      }
      
      // Create notification
      const notification = document.createElement('div');
      notification.style.cssText = `
        background-color: #10b981;
        color: white;
        padding: 12px 16px;
        border-radius: 6px;
        margin-bottom: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transform: translateY(20px);
        opacity: 0;
        transition: all 0.3s ease;
      `;
      notification.textContent = message;
      
      // Add to container
      container.appendChild(notification);
      
      // Animate in
      setTimeout(() => {
        notification.style.transform = 'translateY(0)';
        notification.style.opacity = '1';
      }, 10);
      
      // Animate out and remove
      setTimeout(() => {
        notification.style.transform = 'translateY(20px)';
        notification.style.opacity = '0';
        
        setTimeout(() => {
          container.removeChild(notification);
        }, 300);
      }, 3000);
    }
    
    // On load, check if we have a previously selected game type
    window.addEventListener('DOMContentLoaded', () => {
      try {
        const gameData = localStorage.getItem('slotai_game_data');
        if (gameData) {
          const parsedData = JSON.parse(gameData);
          if (parsedData.selectedGameType) {
            const element = document.querySelector(`.game-card[data-type="${parsedData.selectedGameType}"]`);
            if (element) {
              // Simulate a click to select it
              selectGameType(element, parsedData.selectedGameType);
            }
          }
        }
      } catch (e) {
        console.error('Error loading saved game type:', e);
      }
    });
  </script>
</body>
</html>