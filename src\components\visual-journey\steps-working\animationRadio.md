
animation radio button 

 <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-4">
                    <div className="px-4 py-3 border-b border-gray-200 border-l-4 border-l-purple-500 bg-gray-50">
                      <h3 className="font-semibold text-gray-900">Animation Type</h3>
                    </div>
                    <div className='flex items-center justify-center p-2 gap-10'>
                      <div>
                        <label style={{ display: 'flex', alignItems: 'center', gap: '8px', cursor: 'pointer' }}>
                          <input
                            type="radio"
                            checked={animationType === 'still'}
                            onChange={() => {
                              console.log('🎬 RADIO CLICKED: Animation Type changed to STILL');
                              setAnimationType('still');
                            }}
                            style={{ accentColor: '#8b5cf6' }}
                          />
                          <span>Still Image</span>
                        </label>
                      </div>
                      <div>
                        <label style={{ display: 'flex', alignItems: 'center', gap: '8px', cursor: 'pointer' }}>
                          <input
                            type="radio"
                            checked={animationType === 'animation'}
                            onChange={() => {
                              console.log('🎬 RADIO CLICKED: Animation Type changed to ANIMATION');
                              setAnimationType('animation');
                            }}
                            style={{ accentColor: '#8b5cf6' }}
                          />
                          <span>Animated Sprite Sheet (5x5)</span>
                        </label>
                      </div>
                    </div>
                    {animationType === 'animation' && (
                      <div className="px-4 pb-3">
                        <div className="text-xs text-purple-700 bg-purple-50 p-2 rounded border border-purple-200">
                          🎬 Animation mode will generate a 5x5 sprite sheet (25 frames) for smooth animation effects
                        </div>
                      </div>
                    )}
                  </div>