{"name": "<PERSON><PERSON>", "private": true, "version": "0.1.0", "scripts": {"dev": "node --max-old-space-size=8192 ./node_modules/vite/bin/vite.js", "dev:vite": "npx vite", "dev:netlify": "netlify dev", "vite-only": "npx vite", "build": "node --max-old-space-size=8192 node_modules/.bin/vite build", "lint": "eslint .", "typecheck": "tsc --noEmit", "preview": "vite preview", "serve": "node static-server.cjs", "serve-dev": "node dev-server.cjs", "render-build": "npm ci && npm run build", "render-start": "node static-server.cjs"}, "dependencies": {"@anthropic-ai/sdk": "^0.18.0", "@google/generative-ai": "^0.24.0", "@netlify/functions": "^2.6.0", "@pixi/filter-glow": "^5.2.1", "@pixi/particle-emitter": "^5.0.8", "axios": "^1.8.4", "bezier-easing": "^2.1.0", "canvas2video": "^1.1.4", "chroma-js": "^3.1.2", "clsx": "^2.1.0", "convex-hull": "^1.0.3", "cors": "^2.8.5", "dotenv": "^16.4.7", "earcut": "^3.0.1", "express": "^4.21.2", "framer-motion": "^12.6.2", "gsap": "^3.12.7", "howler": "^2.2.4", "http-proxy-middleware": "^2.0.7", "jszip": "^3.10.1", "keen-slider": "^6.8.6", "lucide-react": "^0.523.0", "matter-js": "^0.20.0", "node-fetch": "^3.3.2", "openai": "^4.28.0", "paper": "^0.12.18", "pixi.js": "^7.4.3", "polygon-clipping": "^0.15.7", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^7.6.2", "recharts": "^2.12.2", "simplify-js": "^1.2.4", "stats.js": "^0.17.0", "swiper": "^11.2.6", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/howler": "^2.2.12", "@types/jszip": "^3.4.0", "@types/react": "^18.2.41", "@types/react-dom": "^18.2.15", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.21", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "netlify-cli": "^15.0.0", "npm-run-all": "^4.1.5", "postcss": "^8.5.6", "tailwindcss": "^3.4.16", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^7.0.4"}}