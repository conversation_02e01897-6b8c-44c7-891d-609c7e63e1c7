[build]
  command = "npm run build"
  publish = "dist"
  functions = "netlify/functions"

[dev]
  command = "npm run dev:vite"
  port = 9999
  targetPort = 5173
  publish = "dist"
  framework = "#custom"
  autoLaunch = false

[[redirects]]
  from = "/api/*"
  to = "/.netlify/functions/:splat"
  status = 200

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

[functions]
  node_bundler = "esbuild"
  included_files = []
  external_node_modules = ["@anthropic-ai/sdk", "openai"]
  
[functions."generate-images"]
  timeout = 28 # Increased slightly but still under 30s limit
  memory = 1024 # Increased memory allocation
  maxDuration = 28 # Match timeout

[[edge_functions]]
  function = "*"
  path = "/*"