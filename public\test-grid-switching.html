<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Grid Switching Test - SlotAI</title>
  <style>
    body {
      margin: 0;
      padding: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: #0a0a0a;
      color: #e0e0e0;
      height: 100vh;
      overflow: hidden;
    }
    
    .test-container {
      height: 100vh;
      display: flex;
      flex-direction: column;
    }
    
    .test-header {
      background: #1a1a2e;
      padding: 1rem;
      border-bottom: 1px solid #333;
      flex-shrink: 0;
    }
    
    .test-header h1 {
      margin: 0 0 0.5rem 0;
      font-size: 1.5rem;
      color: #9b59b6;
    }
    
    .controls {
      display: flex;
      gap: 1rem;
      align-items: center;
      flex-wrap: wrap;
    }
    
    .control-group {
      display: flex;
      gap: 0.5rem;
      align-items: center;
      background: #16213e;
      padding: 0.5rem;
      border-radius: 0.5rem;
    }
    
    .control-group label {
      font-size: 0.875rem;
      color: #999;
    }
    
    button {
      padding: 0.5rem 1rem;
      background: #2d3748;
      color: white;
      border: none;
      border-radius: 0.25rem;
      cursor: pointer;
      font-size: 0.875rem;
      transition: all 0.2s;
    }
    
    button:hover {
      background: #4a5568;
    }
    
    button.active {
      background: #9b59b6;
    }
    
    select {
      padding: 0.5rem;
      background: #2d3748;
      color: white;
      border: 1px solid #4a5568;
      border-radius: 0.25rem;
      font-size: 0.875rem;
    }
    
    .preview-wrapper {
      flex: 1;
      position: relative;
      overflow: hidden;
      background: #000;
    }
    
    #preview-frame {
      width: 100%;
      height: 100%;
      border: none;
      transition: all 0.3s ease;
    }
    
    .device-frame {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .device-frame.desktop {
      width: 90%;
      height: 90%;
    }
    
    .device-frame.mobile-portrait {
      width: 375px;
      height: 667px;
      border: 2px solid #333;
      border-radius: 20px;
      padding: 10px;
      background: #222;
    }
    
    .device-frame.mobile-landscape {
      width: 667px;
      height: 375px;
      border: 2px solid #333;
      border-radius: 20px;
      padding: 10px;
      background: #222;
    }
    
    .status {
      position: absolute;
      top: 1rem;
      right: 1rem;
      background: rgba(0, 0, 0, 0.8);
      padding: 0.75rem;
      border-radius: 0.5rem;
      font-size: 0.875rem;
      font-family: monospace;
      backdrop-filter: blur(10px);
      border: 1px solid #333;
    }
    
    .status-item {
      display: flex;
      justify-content: space-between;
      gap: 1rem;
      margin-bottom: 0.25rem;
    }
    
    .status-item:last-child {
      margin-bottom: 0;
    }
    
    .status-label {
      color: #999;
    }
    
    .status-value {
      color: #9b59b6;
      font-weight: bold;
    }
    
    .console-log {
      position: absolute;
      bottom: 1rem;
      left: 1rem;
      right: 1rem;
      max-height: 200px;
      overflow-y: auto;
      background: rgba(0, 0, 0, 0.9);
      border: 1px solid #333;
      border-radius: 0.5rem;
      padding: 0.75rem;
      font-family: monospace;
      font-size: 0.75rem;
      backdrop-filter: blur(10px);
    }
    
    .log-entry {
      margin-bottom: 0.25rem;
      color: #999;
    }
    
    .log-entry.info { color: #4fc3f7; }
    .log-entry.warn { color: #ffb74d; }
    .log-entry.error { color: #e57373; }
  </style>
</head>
<body>
  <div class="test-container">
    <div class="test-header">
      <h1>Grid Switching Test</h1>
      <div class="controls">
        <div class="control-group">
          <label>View Mode:</label>
          <button id="desktop-btn" class="active" onclick="setViewMode('desktop')">Desktop</button>
          <button id="mobile-portrait-btn" onclick="setViewMode('mobile-portrait')">Mobile Portrait</button>
          <button id="mobile-landscape-btn" onclick="setViewMode('mobile-landscape')">Mobile Landscape</button>
        </div>
        
        <div class="control-group">
          <label>Grid Size:</label>
          <select id="grid-select" onchange="updateGridSize()">
            <option value="5x3">5×3 (Standard)</option>
            <option value="3x3">3×3 (Classic)</option>
            <option value="6x4">6×4 (Megaways)</option>
          </select>
        </div>
        
        <div class="control-group">
          <label>Step:</label>
          <button onclick="navigateToStep(2)">Step 3</button>
          <button onclick="navigateToStep(3)">Step 4</button>
          <button onclick="navigateToStep(4)">Step 5</button>
        </div>
        
        <div class="control-group">
          <button onclick="clearConsole()">Clear Console</button>
          <button onclick="location.reload()">Reload</button>
        </div>
      </div>
    </div>
    
    <div class="preview-wrapper">
      <div id="device-frame" class="device-frame desktop">
        <iframe id="preview-frame" src="/"></iframe>
      </div>
      
      <div class="status">
        <div class="status-item">
          <span class="status-label">Mode:</span>
          <span class="status-value" id="status-mode">Desktop</span>
        </div>
        <div class="status-item">
          <span class="status-label">Grid:</span>
          <span class="status-value" id="status-grid">5×3</span>
        </div>
        <div class="status-item">
          <span class="status-label">Frame:</span>
          <span class="status-value" id="status-frame">-</span>
        </div>
        <div class="status-item">
          <span class="status-label">Step:</span>
          <span class="status-value" id="status-step">-</span>
        </div>
      </div>
      
      <div class="console-log" id="console-log"></div>
    </div>
  </div>
  
  <script>
    let currentMode = 'desktop';
    let currentGrid = { reels: 5, rows: 3 };
    let currentStep = null;
    const logs = [];
    
    // Setup iframe message listener
    const iframe = document.getElementById('preview-frame');
    
    window.addEventListener('message', (event) => {
      if (event.source === iframe.contentWindow) {
        handleIframeMessage(event.data);
      }
    });
    
    function handleIframeMessage(data) {
      if (data.type === 'console') {
        addLog(data.level || 'info', data.message);
      } else if (data.type === 'navigation') {
        currentStep = data.step;
        updateStatus();
      } else if (data.type === 'grid-update') {
        currentGrid = data.grid;
        updateStatus();
      }
    }
    
    function addLog(level, message) {
      const time = new Date().toLocaleTimeString();
      logs.push({ time, level, message });
      
      if (logs.length > 100) {
        logs.shift();
      }
      
      renderLogs();
    }
    
    function renderLogs() {
      const container = document.getElementById('console-log');
      container.innerHTML = logs.map(log => 
        `<div class="log-entry ${log.level}">[${log.time}] ${log.message}</div>`
      ).join('');
      container.scrollTop = container.scrollHeight;
    }
    
    function clearConsole() {
      logs.length = 0;
      renderLogs();
    }
    
    function setViewMode(mode) {
      currentMode = mode;
      
      // Update button states
      document.querySelectorAll('.control-group button').forEach(btn => {
        if (btn.id && btn.id.includes('-btn')) {
          btn.classList.remove('active');
        }
      });
      document.getElementById(mode + '-btn').classList.add('active');
      
      // Update device frame
      const deviceFrame = document.getElementById('device-frame');
      deviceFrame.className = 'device-frame ' + mode;
      
      // Send message to iframe
      iframe.contentWindow.postMessage({
        type: 'set-view-mode',
        mode: mode,
        orientation: mode.includes('landscape') ? 'landscape' : 'portrait',
        isMobile: mode !== 'desktop'
      }, '*');
      
      updateStatus();
      addLog('info', `View mode changed to ${mode}`);
    }
    
    function updateGridSize() {
      const value = document.getElementById('grid-select').value;
      const [reels, rows] = value.split('x').map(Number);
      currentGrid = { reels, rows };
      
      // Send message to iframe
      iframe.contentWindow.postMessage({
        type: 'update-grid',
        reels: reels,
        rows: rows
      }, '*');
      
      updateStatus();
      addLog('info', `Grid size changed to ${reels}×${rows}`);
    }
    
    function navigateToStep(step) {
      currentStep = step;
      iframe.src = `/#/slot/step/${step}`;
      updateStatus();
      addLog('info', `Navigated to Step ${step + 1}`);
    }
    
    function updateStatus() {
      document.getElementById('status-mode').textContent = 
        currentMode.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase());
      document.getElementById('status-grid').textContent = 
        `${currentGrid.reels}×${currentGrid.rows}`;
      document.getElementById('status-frame').textContent = 
        `${iframe.clientWidth}×${iframe.clientHeight}`;
      document.getElementById('status-step').textContent = 
        currentStep ? `Step ${currentStep + 1}` : '-';
    }
    
    // Initial setup
    iframe.onload = () => {
      setTimeout(() => {
        navigateToStep(2);
        addLog('info', 'Test environment loaded');
        
        // Inject console interceptor
        iframe.contentWindow.eval(`
          const originalLog = console.log;
          const originalWarn = console.warn;
          const originalError = console.error;
          
          console.log = (...args) => {
            originalLog(...args);
            window.parent.postMessage({
              type: 'console',
              level: 'info',
              message: args.join(' ')
            }, '*');
          };
          
          console.warn = (...args) => {
            originalWarn(...args);
            window.parent.postMessage({
              type: 'console',
              level: 'warn',
              message: args.join(' ')
            }, '*');
          };
          
          console.error = (...args) => {
            originalError(...args);
            window.parent.postMessage({
              type: 'console',
              level: 'error',
              message: args.join(' ')
            }, '*');
          };
        `);
      }, 1000);
    };
    
    // Update frame size on resize
    window.addEventListener('resize', updateStatus);
    
    // Initial status update
    updateStatus();
  </script>
</body>
</html>