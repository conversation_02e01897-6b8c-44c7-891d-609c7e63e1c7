<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>FinalSymbolGrid Test</title>
  <style>
    body, html {
      margin: 0;
      padding: 0;
      height: 100%;
      font-family: Arial, sans-serif;
    }
    .container {
      display: flex;
      flex-direction: column;
      height: 100%;
    }
    .controls {
      padding: 10px;
      background: #f0f0f0;
      border-bottom: 1px solid #ccc;
    }
    .grid-container {
      flex: 1;
      position: relative;
      overflow: hidden;
    }
    .test-grid {
      width: 100%;
      height: 100%;
      background: #000;
    }
    .status {
      position: absolute;
      top: 10px;
      left: 10px;
      padding: 8px 12px;
      background: rgba(0,0,0,0.7);
      color: #fff;
      border-radius: 4px;
      z-index: 100;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="controls">
      <h2>FinalSymbolGrid Direct Test</h2>
      <div>
        <button id="loadGrid">Load Grid</button>
        <button id="toggleSize">Toggle Container Size</button>
        <label>
          <input type="checkbox" id="useTestSymbols" checked>
          Use Test Symbols
        </label>
      </div>
    </div>
    <div class="grid-container">
      <div id="grid" class="test-grid"></div>
      <div class="status" id="status">Ready</div>
    </div>
  </div>

  <!-- Load PIXI.js -->
  <script src="https://pixijs.download/v7.3.2/pixi.min.js"></script>
  <!-- Load GSAP for animations -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>

  <script>
    // FinalSymbolGrid implementation
    class FinalSymbolGrid {
      constructor(container, options = {}) {
        this.container = container;
        this.options = Object.assign({
          rows: 3,
          cols: 5,
          debug: true,
          showGrid: true,
          hasIdleAnimation: false,
          defaultSymbols: []
        }, options);
        
        this.appRef = null;
        this.animationsRef = [];
        this.spritesRef = [];
        this.loadedTexturesRef = [];
        this.gridContainerRef = null;
        
        this.hasInitialized = false;
        this.containerSize = { width: 0, height: 0 };
        this.hasSufficientSize = false;
        this.initializationAttempts = 0;
        this.errorState = null;
        this.textureLoadFailed = false;
        this.debugInfo = {};
        
        this.sizeCheckInterval = null;
        this.sizeCheckCount = 0;
        this.useDefaultSize = false;
        
        this.debugOverlayEl = null;
        this.errorOverlayEl = null;
        this.sizeWarningEl = null;
        
        this.createDiagnosticOverlays();
        this.startSizePolling();
      }
      
      // Debug logging
      debugLog(message, data) {
        if (this.options.debug) {
          console.debug(`[FinalSymbolGrid] ${message}`, data !== undefined ? data : '');
        }
      }
      
      // Update debug info display
      updateDebugInfo(info) {
        this.debugInfo = {...this.debugInfo, ...info};
        this.updateDebugOverlay();
      }
      
      // Create diagnostic overlays
      createDiagnosticOverlays() {
        // Size warning overlay
        this.sizeWarningEl = document.createElement('div');
        this.sizeWarningEl.style.position = 'absolute';
        this.sizeWarningEl.style.top = '0';
        this.sizeWarningEl.style.left = '0';
        this.sizeWarningEl.style.width = '100%';
        this.sizeWarningEl.style.height = '100%';
        this.sizeWarningEl.style.background = 'rgba(255, 0, 0, 0.7)';
        this.sizeWarningEl.style.color = 'white';
        this.sizeWarningEl.style.display = 'flex';
        this.sizeWarningEl.style.flexDirection = 'column';
        this.sizeWarningEl.style.justifyContent = 'center';
        this.sizeWarningEl.style.alignItems = 'center';
        this.sizeWarningEl.style.zIndex = '10';
        this.sizeWarningEl.style.fontSize = '16px';
        this.sizeWarningEl.style.padding = '20px';
        this.sizeWarningEl.style.textAlign = 'center';
        this.sizeWarningEl.innerHTML = `
          <strong style="font-size: 20px; margin-bottom: 10px;">
            FinalSymbolGrid failed to get valid container dimensions
          </strong>
          <div>Current size: 0x0</div>
          <div>Attempt: 0</div>
          <div style="margin-top: 10px;">
            Waiting for sufficient container size...
          </div>
        `;
        this.sizeWarningEl.style.display = 'none';
        this.container.appendChild(this.sizeWarningEl);
        
        // Error overlay
        this.errorOverlayEl = document.createElement('div');
        this.errorOverlayEl.style.position = 'absolute';
        this.errorOverlayEl.style.bottom = '0';
        this.errorOverlayEl.style.left = '0';
        this.errorOverlayEl.style.width = '100%';
        this.errorOverlayEl.style.background = 'rgba(255, 0, 0, 0.8)';
        this.errorOverlayEl.style.color = 'white';
        this.errorOverlayEl.style.padding = '10px';
        this.errorOverlayEl.style.fontSize = '14px';
        this.errorOverlayEl.style.zIndex = '15';
        this.errorOverlayEl.style.display = 'none';
        this.container.appendChild(this.errorOverlayEl);
        
        // Debug overlay
        this.debugOverlayEl = document.createElement('div');
        this.debugOverlayEl.style.position = 'absolute';
        this.debugOverlayEl.style.top = '0';
        this.debugOverlayEl.style.right = '0';
        this.debugOverlayEl.style.background = 'rgba(0, 0, 0, 0.7)';
        this.debugOverlayEl.style.color = 'lime';
        this.debugOverlayEl.style.padding = '10px';
        this.debugOverlayEl.style.fontSize = '12px';
        this.debugOverlayEl.style.fontFamily = 'monospace';
        this.debugOverlayEl.style.maxWidth = '300px';
        this.debugOverlayEl.style.maxHeight = '80%';
        this.debugOverlayEl.style.overflowY = 'auto';
        this.debugOverlayEl.style.zIndex = '20';
        this.debugOverlayEl.style.pointerEvents = 'none';
        if (!this.options.debug) {
          this.debugOverlayEl.style.display = 'none';
        }
        this.container.appendChild(this.debugOverlayEl);
      }
      
      // Update debug overlay
      updateDebugOverlay() {
        if (!this.options.debug || !this.debugOverlayEl) return;
        
        let html = `
          <div><strong>FinalSymbolGrid Diagnostic</strong></div>
          <div>Initialized: ${this.hasInitialized ? 'Yes' : 'No'}</div>
          <div>Size: ${this.containerSize.width}x${this.containerSize.height}</div>
          <div>Sufficient: ${this.hasSufficientSize ? 'Yes' : 'No'}</div>
          <div>Attempts: ${this.initializationAttempts}</div>
          <div>Grid: ${this.options.rows}x${this.options.cols}</div>
          <div>Error: ${this.errorState || 'None'}</div>
          <div>Texture Load Failed: ${this.textureLoadFailed ? 'Yes' : 'No'}</div>
        `;
        
        // Add additional debug info
        for (const [key, value] of Object.entries(this.debugInfo)) {
          html += `<div>${key}: ${typeof value === 'object' ? JSON.stringify(value) : String(value)}</div>`;
        }
        
        this.debugOverlayEl.innerHTML = html;
      }
      
      // Update size warning overlay
      updateSizeWarning() {
        if (!this.sizeWarningEl) return;
        
        const shouldShow = !this.hasSufficientSize || 
                          this.containerSize.width < 100 || 
                          this.containerSize.height < 100;
        
        this.sizeWarningEl.style.display = shouldShow ? 'flex' : 'none';
        
        if (shouldShow) {
          this.sizeWarningEl.querySelector('div:nth-child(2)').textContent = 
            `Current size: ${this.containerSize.width}x${this.containerSize.height}`;
          this.sizeWarningEl.querySelector('div:nth-child(3)').textContent = 
            `Attempt: ${this.sizeCheckCount}`;
        }
      }
      
      // Update error overlay
      setError(error) {
        this.errorState = error;
        
        if (this.errorOverlayEl) {
          if (error) {
            this.errorOverlayEl.innerHTML = `<strong>Error:</strong> ${error}`;
            this.errorOverlayEl.style.display = 'block';
          } else {
            this.errorOverlayEl.style.display = 'none';
          }
        }
        
        this.updateDebugOverlay();
      }
      
      // Start polling for container size
      startSizePolling() {
        this.debugLog("Component mounted, starting size polling...");
        this.sizeCheckCount = 0;
        this.useDefaultSize = false;
        
        this.sizeCheckInterval = setInterval(() => {
          this.sizeCheckCount++;
          
          if (this.container) {
            const width = this.container.clientWidth;
            const height = this.container.clientHeight;
            
            this.debugLog(`Size check #${this.sizeCheckCount}: ${width}x${height}`);
            this.containerSize = { width, height };
            this.updateDebugInfo({ 
              containerWidth: width, 
              containerHeight: height,
              sizeCheckCount: this.sizeCheckCount
            });
            
            // Update size warning
            this.updateSizeWarning();
            
            // Check if dimensions are sufficient
            if (width > 100 && height > 100) {
              this.debugLog(`Found sufficient container size: ${width}x${height}`);
              this.hasSufficientSize = true;
              clearInterval(this.sizeCheckInterval);
              this.initializeGrid();
            } 
            // After 20 attempts (5 seconds), fall back to default size
            else if (this.sizeCheckCount >= 20 && !this.useDefaultSize) {
              this.debugLog("Size polling timed out, falling back to default dimensions 800x600");
              this.containerSize = { width: 800, height: 600 };
              this.hasSufficientSize = true;
              this.useDefaultSize = true;
              clearInterval(this.sizeCheckInterval);
              this.initializeGrid();
            }
          }
        }, 250); // Poll every 250ms
      }
      
      // Initialize the PIXI application and grid
      initializeGrid() {
        if (this.hasInitialized) return;
        
        try {
          this.initializationAttempts++;
          const width = this.options.dimensions?.width || this.containerSize.width;
          const height = this.options.dimensions?.height || this.containerSize.height;
          
          // Always log the specific initialization message
          console.info(`[FinalSymbolGrid] Initialization triggered with dimensions: ${width}x${height}`);
          this.debugLog(`Attempt #${this.initializationAttempts} to initialize PIXI app with dimensions: ${width}x${height}`);
          
          if (width < 100 || height < 100) {
            this.debugLog("Dimensions too small, aborting initialization");
            this.setError("Container dimensions too small");
            return;
          }
          
          this.updateDebugInfo({ 
            appWidth: width, 
            appHeight: height,
            initAttempt: this.initializationAttempts,
            gridRows: this.options.rows,
            gridCols: this.options.cols,
            symbolCount: this.options.defaultSymbols.length
          });
          
          // Create PIXI application
          if (this.container) {
            // Destroy any existing app
            if (this.appRef) {
              this.debugLog("Destroying existing PIXI app before re-initialization");
              this.appRef.destroy(true);
            }
            
            // Create new PIXI app
            const app = new PIXI.Application({
              width,
              height,
              backgroundColor: 0x000000,
              antialias: true,
              resolution: window.devicePixelRatio || 1,
            });
            
            this.container.innerHTML = '';
            this.container.appendChild(app.view);
            this.appRef = app;
            
            // Re-add overlays after clearing innerHTML
            this.container.appendChild(this.sizeWarningEl);
            this.container.appendChild(this.errorOverlayEl);
            this.container.appendChild(this.debugOverlayEl);
            
            // Create the symbol grid
            this.createSymbolGrid(width, height);
            this.hasInitialized = true;
            this.updateDebugOverlay();
            this.updateSizeWarning();
            
            if (this.options.onGridReady) {
              this.options.onGridReady();
            }
          }
        } catch (error) {
          console.error("[FinalSymbolGrid] Error initializing PIXI application:", error);
          this.setError(`Initialization error: ${error instanceof Error ? error.message : 'Unknown error'}`);
          this.updateDebugInfo({ error: error instanceof Error ? error.message : 'Unknown error' });
        }
      }
      
      // Create the symbol grid
      async createSymbolGrid(width, height) {
        if (!this.appRef) {
          this.debugLog("Cannot create grid: PIXI app not initialized");
          return;
        }
        
        try {
          this.debugLog("Creating symbol grid with dimensions:", { width, height, rows: this.options.rows, cols: this.options.cols });
          
          // Calculate grid and cell dimensions
          const { 
            xOffset = 0, 
            yOffset = 0, 
            scale = 1, 
            marginTop = 0, 
            marginBottom = 0, 
            marginLeft = 0, 
            marginRight = 0 
          } = this.options.frameConfig || {};
          
          const gridWidth = width - marginLeft - marginRight;
          const gridHeight = height - marginTop - marginBottom;
          const cellWidth = gridWidth / this.options.cols;
          const cellHeight = gridHeight / this.options.rows;
          
          this.updateDebugInfo({ 
            gridWidth, 
            gridHeight, 
            cellWidth, 
            cellHeight 
          });
          
          // Create grid container
          const gridContainer = new PIXI.Container();
          gridContainer.x = width / 2 + xOffset;
          gridContainer.y = height / 2 + yOffset;
          gridContainer.scale.set(scale);
          this.appRef.stage.addChild(gridContainer);
          this.gridContainerRef = gridContainer;
          
          // Show grid lines if requested
          if (this.options.showGrid) {
            this.debugLog("Drawing grid lines");
            const gridGraphics = new PIXI.Graphics();
            gridGraphics.lineStyle(2, 0xFFFFFF, 0.3);
            
            // Draw horizontal lines
            for (let r = 0; r <= this.options.rows; r++) {
              const y = r * cellHeight - (gridHeight / 2);
              gridGraphics.moveTo(-gridWidth / 2, y);
              gridGraphics.lineTo(gridWidth / 2, y);
            }
            
            // Draw vertical lines
            for (let c = 0; c <= this.options.cols; c++) {
              const x = c * cellWidth - (gridWidth / 2);
              gridGraphics.moveTo(x, -gridHeight / 2);
              gridGraphics.lineTo(x, gridHeight / 2);
            }
            
            gridContainer.addChild(gridGraphics);
          }
          
          // Create test cell grid
          this.createTestCellGrid(gridContainer, cellWidth, cellHeight);
          this.updateDebugInfo({ symbolSource: 'test_cells' });
          
        } catch (error) {
          console.error("[FinalSymbolGrid] Error in createSymbolGrid:", error);
          this.setError(`Grid creation error: ${error instanceof Error ? error.message : 'Unknown error'}`);
          
          // Create test cells as fallback if grid creation fails
          if (this.appRef && this.gridContainerRef) {
            this.debugLog("Creating emergency TEST CELL grid after error");
            const cellWidth = width / this.options.cols;
            const cellHeight = height / this.options.rows;
            this.createTestCellGrid(this.gridContainerRef, cellWidth, cellHeight);
            this.updateDebugInfo({ symbolSource: 'emergency_test_cells' });
          }
        }
      }
      
      // Create a test cell
      createTestCell(gridContainer, col, row, cellWidth, cellHeight) {
        this.debugLog(`Creating TEST CELL at (${col},${row})`);
        
        // Create a container for the cell
        const cellContainer = new PIXI.Container();
        
        // Position the cell
        const x = (col - (this.options.cols - 1) / 2) * cellWidth;
        const y = (row - (this.options.rows - 1) / 2) * cellHeight;
        cellContainer.position.set(x, y);
        
        // Create a bright background rectangle
        const graphics = new PIXI.Graphics();
        graphics.lineStyle(3, 0xFFFFFF, 1);
        graphics.beginFill(0xFF3333, 0.7);
        graphics.drawRect(-cellWidth/2 * 0.9, -cellHeight/2 * 0.9, cellWidth * 0.9, cellHeight * 0.9);
        graphics.endFill();
        
        // Add TEST CELL text
        const text = new PIXI.Text(`TEST CELL\n(${col},${row})`, {
          fontFamily: 'Arial',
          fontSize: cellWidth * 0.12,
          fill: 0xFFFFFF,
          align: 'center',
          fontWeight: 'bold',
        });
        text.anchor.set(0.5);
        
        cellContainer.addChild(graphics);
        cellContainer.addChild(text);
        gridContainer.addChild(cellContainer);
        
        return cellContainer;
      }
      
      // Create a grid of test cells
      createTestCellGrid(gridContainer, cellWidth, cellHeight) {
        this.debugLog("Creating complete TEST CELL grid");
        
        for (let r = 0; r < this.options.rows; r++) {
          for (let c = 0; c < this.options.cols; c++) {
            this.createTestCell(
              gridContainer,
              c,
              r,
              cellWidth,
              cellHeight
            );
          }
        }
      }
      
      // Cleanup resources
      destroy() {
        this.debugLog("Component destroying, cleaning up resources");
        
        // Clear size polling interval
        if (this.sizeCheckInterval) {
          clearInterval(this.sizeCheckInterval);
        }
        
        // Stop all animations
        this.animationsRef.forEach((anim) => {
          anim.kill();
        });
        this.animationsRef = [];
        
        // Destroy sprites
        this.spritesRef.forEach((sprite) => {
          if (sprite && !sprite.destroyed) {
            sprite.destroy({ children: true, texture: false, baseTexture: false });
          }
        });
        this.spritesRef = [];
        
        // Destroy textures
        this.loadedTexturesRef.forEach((texture) => {
          if (texture && !texture.destroyed) {
            texture.destroy(true);
          }
        });
        this.loadedTexturesRef = [];
        
        // Destroy grid container
        if (this.gridContainerRef) {
          this.gridContainerRef.destroy({ children: true });
          this.gridContainerRef = null;
        }
        
        // Destroy PIXI application
        if (this.appRef) {
          this.appRef.destroy(true);
          this.appRef = null;
        }
        
        // Remove overlays
        if (this.sizeWarningEl && this.sizeWarningEl.parentNode) {
          this.sizeWarningEl.parentNode.removeChild(this.sizeWarningEl);
        }
        
        if (this.errorOverlayEl && this.errorOverlayEl.parentNode) {
          this.errorOverlayEl.parentNode.removeChild(this.errorOverlayEl);
        }
        
        if (this.debugOverlayEl && this.debugOverlayEl.parentNode) {
          this.debugOverlayEl.parentNode.removeChild(this.debugOverlayEl);
        }
      }
    }
    
    // Test-specific code
    document.addEventListener('DOMContentLoaded', () => {
      const gridContainer = document.getElementById('grid');
      const statusEl = document.getElementById('status');
      const loadBtn = document.getElementById('loadGrid');
      const toggleSizeBtn = document.getElementById('toggleSize');
      const useTestSymbolsCheck = document.getElementById('useTestSymbols');
      
      let symbolGrid = null;
      let isSizeSmall = false;
      
      function updateStatus(message) {
        statusEl.textContent = message;
      }
      
      loadBtn.addEventListener('click', () => {
        updateStatus('Loading grid...');
        
        // Destroy existing grid if any
        if (symbolGrid) {
          symbolGrid.destroy();
          symbolGrid = null;
        }
        
        // Create new grid
        symbolGrid = new FinalSymbolGrid(gridContainer, {
          rows: 3,
          cols: 5,
          debug: true,
          showGrid: true,
          onGridReady: () => {
            updateStatus('Grid ready');
          }
        });
      });
      
      toggleSizeBtn.addEventListener('click', () => {
        isSizeSmall = !isSizeSmall;
        
        if (isSizeSmall) {
          gridContainer.style.width = '50px';
          gridContainer.style.height = '50px';
          updateStatus('Container size: 50x50');
        } else {
          gridContainer.style.width = '100%';
          gridContainer.style.height = '100%';
          updateStatus('Container size: 100%');
        }
        
        // Force grid to reinitialize if it exists
        if (symbolGrid) {
          symbolGrid.destroy();
          symbolGrid = null;
          
          // Recreate after a short delay
          setTimeout(() => {
            symbolGrid = new FinalSymbolGrid(gridContainer, {
              rows: 3,
              cols: 5,
              debug: true,
              showGrid: true,
              onGridReady: () => {
                updateStatus('Grid ready after resize');
              }
            });
          }, 500);
        }
      });
      
      // Initial load
      updateStatus('Click "Load Grid" to start');
    });
  </script>
</body>
</html>