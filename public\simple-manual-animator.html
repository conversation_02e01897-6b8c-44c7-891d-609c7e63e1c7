<!DOCTYPE html>
<html>
<head>
    <title>Simple Manual Frame Animator</title>
    <style>
        body { 
            font-family: Arial; 
            background: #222; 
            color: white; 
            text-align: center; 
            padding: 20px; 
        }
        .upload-area { 
            background: #444; 
            padding: 20px; 
            margin: 20px; 
            border-radius: 10px; 
        }
        .frames-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 10px;
            margin: 20px 0;
        }
        .frame-slot { 
            border: 2px dashed #666; 
            padding: 10px;
            margin: 5px; 
            cursor: pointer; 
            border-radius: 5px;
            min-height: 120px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        .frame-slot.filled { 
            border-color: gold; 
            background: rgba(255,215,0,0.1); 
        }
        .frame-slot img { 
            width: 80px; 
            height: 80px; 
            margin: 5px; 
        }
        canvas { 
            border: 2px solid #666; 
            background: transparent; 
            margin: 20px; 
        }
        button { 
            background: gold; 
            color: black; 
            border: none; 
            padding: 10px 20px; 
            margin: 5px; 
            border-radius: 5px; 
            cursor: pointer; 
        }
        input[type="range"] { 
            width: 200px; 
        }
        .hidden { display: none; }
    </style>
</head>
<body>
    <h1>🔴 Simple Manual Frame Animator</h1>
    
    <div class="upload-area">
        <h3>Upload Your 16 Gem Frames</h3>
        <div class="frames-grid" id="frameSlots"></div>
        <button onclick="clearAll()">Clear All</button>
        <button onclick="testPattern()">Test Pattern</button>
        <div id="status">Ready to upload frames. Click any slot above.</div>
    </div>
    
    <div class="upload-area">
        <h3>Animation Preview</h3>
        <canvas id="canvas" width="300" height="300"></canvas><br>
        <button onclick="play()">Play</button>
        <button onclick="stop()">Stop</button>
        <button onclick="exportRef()">Export Reference</button><br>
        Speed: <input type="range" id="speed" min="50" max="500" value="150"> <span id="speedText">150ms</span>
        <div id="info">No frames loaded</div>
    </div>

    <script>
        var frames = [];
        var playing = false;
        var frameIndex = 0;
        var interval = null;
        var speed = 150;

        // Create 16 upload slots immediately
        function createSlots() {
            var container = document.getElementById('frameSlots');
            container.innerHTML = '';
            frames = [];
            
            for (var i = 0; i < 16; i++) {
                var slot = document.createElement('div');
                slot.className = 'frame-slot';
                slot.innerHTML = '<div>Frame ' + (i + 1) + '<br>Click to upload</div>';
                
                var input = document.createElement('input');
                input.type = 'file';
                input.accept = 'image/*';
                input.className = 'hidden';
                input.setAttribute('data-index', i);
                
                slot.onclick = function() { 
                    this.querySelector('input').click(); 
                };
                
                input.onchange = function() {
                    var index = parseInt(this.getAttribute('data-index'));
                    var file = this.files[0];
                    if (file) {
                        loadFrame(index, file);
                    }
                };
                
                slot.appendChild(input);
                container.appendChild(slot);
                frames.push(null);
            }
        }

        function loadFrame(index, file) {
            var reader = new FileReader();
            reader.onload = function(e) {
                var img = new Image();
                img.onload = function() {
                    frames[index] = img;
                    updateSlot(index, img);
                    updateStatus();
                };
                img.src = e.target.result;
            };
            reader.readAsDataURL(file);
        }

        function updateSlot(index, img) {
            var slots = document.querySelectorAll('.frame-slot');
            var slot = slots[index];
            slot.className = 'frame-slot filled';
            slot.innerHTML = '<div>Frame ' + (index + 1) + '</div>';
            
            var imgEl = document.createElement('img');
            imgEl.src = img.src;
            slot.appendChild(imgEl);
            
            var input = document.createElement('input');
            input.type = 'file';
            input.accept = 'image/*';
            input.className = 'hidden';
            input.setAttribute('data-index', index);
            input.onchange = function() {
                var file = this.files[0];
                if (file) loadFrame(index, file);
            };
            slot.appendChild(input);
        }

        function updateStatus() {
            var loaded = frames.filter(f => f !== null).length;
            document.getElementById('status').textContent = loaded + '/16 frames loaded';
            document.getElementById('info').textContent = loaded + ' frames loaded';
        }

        function clearAll() {
            createSlots();
            stop();
            updateStatus();
        }

        function testPattern() {
            for (var i = 0; i < 8; i++) {
                var canvas = document.createElement('canvas');
                canvas.width = 100;
                canvas.height = 100;
                var ctx = canvas.getContext('2d');
                
                var angle = (i / 8) * Math.PI * 2;
                ctx.fillStyle = 'red';
                ctx.beginPath();
                ctx.arc(50, 50, 30, 0, Math.PI * 2);
                ctx.fill();
                
                ctx.fillStyle = 'white';
                ctx.beginPath();
                ctx.arc(50 + Math.cos(angle) * 15, 50 + Math.sin(angle) * 15, 8, 0, Math.PI * 2);
                ctx.fill();
                
                var img = new Image();
                img.onload = (function(index) {
                    return function() {
                        frames[index] = this;
                        updateSlot(index, this);
                        if (index === 7) updateStatus();
                    };
                })(i);
                img.src = canvas.toDataURL();
            }
        }

        function play() {
            var validFrames = frames.filter(f => f !== null);
            if (validFrames.length < 2) return;
            
            playing = true;
            interval = setInterval(function() {
                // Find next valid frame
                var attempts = 0;
                do {
                    frameIndex = (frameIndex + 1) % frames.length;
                    attempts++;
                } while (frames[frameIndex] === null && attempts < frames.length);
                
                if (frames[frameIndex] !== null) {
                    draw();
                }
            }, speed);
        }

        function stop() {
            playing = false;
            if (interval) clearInterval(interval);
        }

        function draw() {
            var canvas = document.getElementById('canvas');
            var ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            if (frames[frameIndex]) {
                var centerX = canvas.width / 2;
                var centerY = canvas.height / 2;
                var size = 100;
                ctx.drawImage(frames[frameIndex], centerX - size/2, centerY - size/2, size, size);
            }
        }

        function exportRef() {
            var validFrames = frames.filter(f => f !== null);
            if (validFrames.length < 2) {
                alert('Need at least 2 frames');
                return;
            }

            var canvas = document.createElement('canvas');
            canvas.width = 150;
            canvas.height = 150;
            var ctx = canvas.getContext('2d');

            var frameData = [];
            for (var i = 0; i < frames.length; i++) {
                if (frames[i] !== null) {
                    ctx.clearRect(0, 0, 150, 150);
                    ctx.drawImage(frames[i], 0, 0, 150, 150);
                    frameData.push(canvas.toDataURL());
                }
            }

            var html = '<!DOCTYPE html><html><head><title>Perfect Gem Reference</title></head>';
            html += '<body style="text-align:center;background:#222;color:white;">';
            html += '<h1>Perfect Gem Animation Reference</h1>';
            html += '<p>This is EXACTLY how the gem should rotate!</p>';
            html += '<canvas id="c" width="150" height="150" style="border:2px solid #666;"></canvas><br><br>';
            html += '<button onclick="toggle()">Pause/Resume</button>';
            html += '<script>';
            html += 'var frames=' + JSON.stringify(frameData) + ';';
            html += 'var canvas=document.getElementById("c");';
            html += 'var ctx=canvas.getContext("2d");';
            html += 'var frame=0,playing=true,interval;';
            html += 'function animate(){';
            html += 'if(!playing)return;';
            html += 'var img=new Image();';
            html += 'img.onload=function(){ctx.clearRect(0,0,150,150);ctx.drawImage(img,0,0);};';
            html += 'img.src=frames[frame];';
            html += 'frame=(frame+1)%frames.length;';
            html += '}';
            html += 'function toggle(){playing=!playing;}';
            html += 'function start(){clearInterval(interval);interval=setInterval(animate,' + speed + ');}';
            html += 'start();';
            html += '</script></body></html>';

            var blob = new Blob([html], {type: 'text/html'});
            var url = URL.createObjectURL(blob);
            var a = document.createElement('a');
            a.href = url;
            a.download = 'perfect-gem-reference.html';
            a.click();
            URL.revokeObjectURL(url);
            
            alert('Perfect animation reference exported!');
        }

        document.getElementById('speed').oninput = function() {
            speed = parseInt(this.value);
            document.getElementById('speedText').textContent = speed + 'ms';
            if (playing) {
                stop();
                play();
            }
        };

        // Initialize immediately
        createSlots();
        updateStatus();
    </script>
</body>
</html>