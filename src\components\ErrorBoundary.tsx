import React from 'react';

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  errorInfo?: React.ErrorInfo;
}

interface ErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
  resetOnPropsChange?: boolean;
}

// React 19 Enhanced Error Boundary with concurrent features support
export class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    // React 19: Enhanced error state with better concurrent support
    return { hasError: true, error };
  }

  componentDidUpdate(prevProps: ErrorBoundaryProps) {
    // React 19: Reset on props change for better recovery
    if (this.props.resetOnPropsChange && 
        this.state.hasError && 
        prevProps.children !== this.props.children) {
      this.setState({ hasError: false, error: undefined, errorInfo: undefined });
    }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // Store error info for React 19 enhanced debugging
    this.setState({ errorInfo });
    
    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
    
    console.error('🛡️ ErrorBoundary caught error:', error, errorInfo);
    
    // React 19: Enhanced concurrent error detection
    const isConcurrentError = error.message && (
      error.message.includes('suspended') ||
      error.message.includes('concurrent') ||
      error.message.includes('startTransition') ||
      error.message.includes('flushSync')
    );
    
    // Don't crash on DOM manipulation errors - Enhanced detection
    const isDOMError = error.message && (
      error.message.includes('removeChild') || 
      error.message.includes('insertBefore') ||
      error.message.includes('appendChild') ||
      error.message.includes('The node to be removed is not a child') ||
      error.message.includes('Cannot read property') && error.message.includes('parentNode') ||
      error.message.includes('Cannot read properties of null') ||
      error.name === 'NotFoundError' ||
      error.name === 'HierarchyRequestError'
    );
    
    // Check if error is from PIXI/animation components
    const isAnimationError = errorInfo.componentStack && (
      errorInfo.componentStack.includes('AutomatedAnimationStudio') ||
      errorInfo.componentStack.includes('ProfessionalPixiRenderer') ||
      errorInfo.componentStack.includes('AnimationTestLab') ||
      errorInfo.componentStack.includes('canvas') ||
      errorInfo.componentStack.includes('data-renderer-container')
    );
    
    if (isDOMError || isAnimationError || isConcurrentError) {
      console.log('🛡️ Suppressing React error - continuing...', {
        errorType: error.name,
        message: error.message,
        isAnimationComponent: isAnimationError,
        isConcurrentError,
        isDOMError
      });
      
      // React 19: Improved error recovery with concurrent support
      setTimeout(() => {
        this.setState({ hasError: false, error: undefined, errorInfo: undefined });
      }, isConcurrentError ? 16 : 100); // Faster recovery for concurrent errors
      return;
    }
  }

  render() {
    if (this.state.hasError && this.state.error) {
      // Enhanced DOM error detection for render method
      const isDOMRenderError = this.state.error.message && (
        this.state.error.message.includes('removeChild') || 
        this.state.error.message.includes('insertBefore') ||
        this.state.error.message.includes('appendChild') ||
        this.state.error.message.includes('The node to be removed is not a child') ||
        this.state.error.name === 'NotFoundError' ||
        this.state.error.name === 'HierarchyRequestError'
      );
      
      // Don't show error UI for DOM manipulation errors - just continue rendering
      if (isDOMRenderError) {
        console.log('🛡️ ErrorBoundary render: Suppressing DOM manipulation error, continuing with children');
        return this.props.children;
      }
      
      return this.props.fallback || (
        <div className="flex items-center justify-center min-h-screen bg-gray-900 text-white">
          <div className="text-center">
            <h2 className="text-xl font-bold mb-4">🛡️ Error Boundary</h2>
            <p className="text-gray-400 mb-4">An error occurred, but the animation system is still running!</p>
            <div className="text-xs text-gray-500 mb-4">
              Error: {this.state.error.name} - {this.state.error.message}
            </div>
            <button 
              onClick={() => this.setState({ hasError: false })}
              className="px-4 py-2 bg-blue-600 rounded hover:bg-blue-700 transition-colors"
            >
              Continue Anyway
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}