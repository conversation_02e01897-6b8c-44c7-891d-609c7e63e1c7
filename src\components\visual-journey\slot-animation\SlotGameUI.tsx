import React, { useState, useContext, useCallback, useEffect, useRef } from 'react';
import { SlotGameContext } from '../contexts/SlotGameContext';
import { useGameStore } from '../../../store';
import { detectDeviceType, onDeviceTypeChange, DeviceType } from '../../../utils/deviceDetection';

// Adaptive button component to handle pressed states
const AdaptiveButton: React.FC<{
  layoutInfo: { name: string; x: number; y: number; scale: number };
  metadata?: any;
  buttonImage?: string;
  pressedImage?: string;
  isSpinning?: boolean;
  onSpin?: () => void;
  onAutoplayToggle?: () => void;
  onMenu?: () => void;
  onSound?: () => void;
  onSettings?: () => void;
}> = ({ layoutInfo, metadata, buttonImage, pressedImage, isSpinning, onSpin, onAutoplayToggle, onMenu, onSound, onSettings }) => {
  const [isPressed, setIsPressed] = useState(false);
  
  if (!buttonImage) return null;
  
  // Calculate button size maintaining aspect ratio
  const originalWidth = metadata?.width || 60;
  const originalHeight = metadata?.height || 60;
  const aspectRatio = originalWidth / originalHeight;
  
  let buttonWidth, buttonHeight;
  if (aspectRatio > 1) {
    // Wider than tall
    buttonWidth = layoutInfo.scale * originalWidth;
    buttonHeight = buttonWidth / aspectRatio;
  } else {
    // Taller than wide or square
    buttonHeight = layoutInfo.scale * originalHeight;
    buttonWidth = buttonHeight * aspectRatio;
  }
  
  // Handle click based on button type
  const handleClick = () => {
    if (layoutInfo.name === 'spinButton' && onSpin) onSpin();
    else if (layoutInfo.name === 'autoplayButton' && onAutoplayToggle) onAutoplayToggle();
    else if (layoutInfo.name === 'menuButton' && onMenu) onMenu();
    else if (layoutInfo.name === 'soundButton' && onSound) onSound();
    else if (layoutInfo.name === 'settingsButton' && onSettings) onSettings();
  };
  
  return (
    <button
      className="adaptive-btn flex items-center justify-center transition-all duration-200 absolute"
      onClick={handleClick}
      onMouseDown={() => setIsPressed(true)}
      onMouseUp={() => setIsPressed(false)}
      onMouseLeave={() => setIsPressed(false)}
      onTouchStart={() => setIsPressed(true)}
      onTouchEnd={() => setIsPressed(false)}
      disabled={isSpinning && layoutInfo.name === 'spinButton'}
      aria-label={layoutInfo.name.replace('Button', '')}
      style={{
        width: `${buttonWidth}px`,
        height: `${buttonHeight}px`,
        background: 'transparent',
        border: 'none',
        padding: '0',
        left: `${layoutInfo.x}px`,
        top: '50%',
        transform: `translate(-50%, -50%) scale(${isPressed ? 0.95 : 1})`,
        zIndex: layoutInfo.name === 'spinButton' ? 200 : 150 // High z-index for buttons
      }}
    >
      <img
        src={isPressed && pressedImage ? pressedImage : buttonImage}
        alt={layoutInfo.name}
        className="w-full h-full object-contain"
        style={{
          filter: 'drop-shadow(0 4px 6px rgba(0, 0, 0, 0.3))',
          borderRadius: metadata?.shape === 'circle' ? '50%' : 
                       metadata?.shape === 'rounded' ? '20%' : '0'
        }}
      />
    </button>
  );
};

interface SlotGameUIProps {
  /** Handler for spin button click */
  onSpin?: () => void;
  /** Handler for autoplay toggle */
  onAutoplayToggle?: () => void;
  /** Handler for max bet button */
  onMaxBet?: () => void;
  /** Handler for slam stop button click */
  onSlamStop?: (reelIndex?: number) => void;
  /** Slam stop status for each reel */
  slamStopStatus?: { [reelIndex: number]: { canStop: boolean, isSpinning: boolean, isStopped: boolean } };
  /** Whether to show slam stop buttons */
  showSlamStop?: boolean;
  /** Current balance value */
  balance?: number;
  /** Current bet value */
  bet?: number;
  /** Current win value */
  win?: number;
  /** Whether currently spinning */
  isSpinning?: boolean;
  /** SVG string for custom spin button */
  spinButtonSvg?: string;
  /** Image URL for custom spin button */
  spinButtonImageUrl?: string;
  /** Additional class names */
  className?: string;
  /** Custom UI button images */
  customButtons?: {
    spinButton?: string;
    autoplayButton?: string;
    menuButton?: string;
    soundButton?: string;
    settingsButton?: string;
  };
  /** Game logo image URL */
  gameLogo?: string;
  /** Game logo position */
  logoPosition?: { x: number; y: number };
  /** Game logo scale percentage */
  logoScale?: number;
  /** Enable interactive logo positioning mode */
  logoPositioningMode?: boolean;
  /** Callback when logo position changes */
  onLogoPositionChange?: (position: { x: number; y: number }) => void;
}

/**
 * Unified Slot Game UI Bar
 * 
 * A standardized control bar for slot games with consistent layout and styling:
 * - Left: Game branding
 * - Center: Action buttons (Auto Play, Max Bet, Spin)
 * - Right: Game values (Win, Balance, Bet)
 * 
 * UI conforms to industry-standard slot game layout conventions,
 * with responsive design that maintains spacing and hierarchy
 */
const SlotGameUI: React.FC<SlotGameUIProps> = ({
  onSpin,
  onAutoplayToggle,
  onMaxBet,
  onSlamStop,
  slamStopStatus = {},
  showSlamStop = false,
  balance = 1000.00,
  bet = 1.00,
  win = 0.00,
  isSpinning: isSpinningProp,
  spinButtonSvg,
  spinButtonImageUrl,
  className = '',
  customButtons,
  gameLogo,
  logoPosition = { x: 0, y: -50 },
  logoScale = 100,
  logoPositioningMode = false,
  onLogoPositionChange,
}) => {
  // Get game state and actions from context - but handle case when provider is not available
  const contextData = useContext(SlotGameContext);
  
  // Local state for when context is not available
  const [localState, setLocalState] = useState({
    showSettings: false,
    showMenu: false,
    isSoundEnabled: true,
    isAutoplayActive: false,
    volume: 100,
    turboMode: false,
    showAnimations: true
  });

  // Logo drag state for interactive positioning
  const [isDraggingLogo, setIsDraggingLogo] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [startPosition, setStartPosition] = useState({ x: 0, y: 0 });
  const [currentDevice, setCurrentDevice] = useState<DeviceType>(detectDeviceType());
  const containerRef = useRef<HTMLDivElement>(null);
  
  // If context is not available, use default values and local state
  const defaultState = {
    balance: 1000,
    bet: 1,
    win: 0,
    isSpinning: false,
    ...localState
  };
  
  const { 
    state = defaultState, 
    spin = () => {
      console.log('[SlotGameUI] Using external spin handler');
      if (onSpin) onSpin();
    },
    toggleAutoplay = () => {
      setLocalState(prev => ({ ...prev, isAutoplayActive: !prev.isAutoplayActive }));
      if (onAutoplayToggle) onAutoplayToggle();
    }, 
    toggleSound = () => {
      setLocalState(prev => ({ ...prev, isSoundEnabled: !prev.isSoundEnabled }));
    },
    toggleSettings = () => {
      setLocalState(prev => ({ ...prev, showSettings: !prev.showSettings }));
    },
    toggleMenu = () => {
      setLocalState(prev => ({ ...prev, showMenu: !prev.showMenu }));
    },
    setBet = () => console.log('[SlotGameUI] Default bet handler'),
    setTurboMode = (enabled: boolean) => {
      setLocalState(prev => ({ ...prev, turboMode: enabled }));
    },
    dispatch = () => console.log('[SlotGameUI] Default dispatch handler')
  } = contextData || {};
  
  // Get UI customization config
  const { config } = useGameStore();
  
  // Get button layout and metadata from config
  const buttonLayout = config?.uiButtonLayout || [];
  const buttonMetadata = config?.uiButtonMetadata || {};

  // Use props values with context as fallback (props take priority for external control)
  const gameBalance = balance !== undefined ? balance : state.balance;
  const gameBet = bet !== undefined ? bet : state.bet;
  const gameWin = win !== undefined ? win : state.win;
  // Always use prop value for isSpinning to avoid conflicts with external game engine
  const isSpinning = isSpinningProp !== undefined ? isSpinningProp : false;
  const isAutoplayActive = state.isAutoplayActive;
  const isSoundEnabled = state.isSoundEnabled;
  
  // Device detection for logo positioning
  useEffect(() => {
    const cleanup = onDeviceTypeChange((newDeviceType: DeviceType) => {
      console.log('[SlotGameUI] Device type changed to:', newDeviceType);
      setCurrentDevice(newDeviceType);
    });

    return cleanup;
  }, []);

  // Get device-specific logo position and scale from game config
  const deviceLogoPosition = config?.logoPositions?.[currentDevice] || logoPosition;
  const deviceLogoScale = config?.logoScales?.[currentDevice] || logoScale;

  // Debug log to check spinning state and logo
  console.log('[SlotGameUI] Component state:', {
    isSpinningProp,
    stateIsSpinning: state.isSpinning,
    finalIsSpinning: isSpinning,
    balance: gameBalance,
    bet: gameBet,
    gameLogo,
    logoPositioningMode,
    deviceLogoPosition,
    deviceLogoScale,
    currentDevice,
    configLogoPositions: config?.logoPositions,
    configLogoScales: config?.logoScales
  });

  // Format currency with 2 decimal places
  const formatCurrency = (value: number): string => {
    return value.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  };
  
  // Debug log to check if buttons are received
  React.useEffect(() => {
    console.log('[SlotGameUI] Component mounted/updated');
    console.log('[SlotGameUI] customButtons:', customButtons);
    console.log('[SlotGameUI] buttonLayout:', buttonLayout);
    console.log('[SlotGameUI] buttonMetadata:', buttonMetadata);
    if (customButtons) {
      console.log('[SlotGameUI] spinButton:', customButtons.spinButton);
      console.log('[SlotGameUI] autoplayButton:', customButtons.autoplayButton);
      console.log('[SlotGameUI] soundButton:', customButtons.soundButton);
      
      // Test loading each button image
      Object.entries(customButtons).forEach(([key, value]) => {
        if (value) {
          // Check if it's a blob URL
          const isBlobUrl = value.startsWith('blob:') || value.startsWith('data:');
          if (isBlobUrl) {
            console.log(`[SlotGameUI] Using blob/data URL for ${key}`);
          }
          
          const img = new window.Image();
          img.onload = () => {
            console.log(`[SlotGameUI] ✓ ${key} loaded successfully from:`, value.substring(0, 50) + '...');
          };
          img.onerror = () => {
            console.error(`[SlotGameUI] ✗ Failed to load ${key} from:`, value);
            // Try alternative paths only for non-blob URLs
            if (!isBlobUrl) {
              const altPath1 = value.startsWith('/') ? value.substring(1) : '/' + value;
              const altPath2 = value.startsWith('/') ? '.' + value : './' + value;
              console.log(`[SlotGameUI] Alternative paths: ${altPath1}, ${altPath2}`);
            }
          };
          img.src = value;
        }
      });
    }
  }, [customButtons]);
  
  // Render button with adaptive sizing
  const renderAdaptiveButton = (buttonName: string, onClick: () => void, defaultContent?: React.ReactNode) => {
    const layoutInfo = buttonLayout.find(b => b.name === buttonName);
    const metadata = buttonMetadata[buttonName];
    const buttonImage = customButtons?.[buttonName];
    
    if (buttonImage && layoutInfo) {
      // Adaptive button with calculated position and size
      const buttonSize = layoutInfo.scale * Math.max(metadata?.width || 80, metadata?.height || 80);
      
      return (
        <button
          className="adaptive-btn flex items-center justify-center transform hover:scale-105 transition-all duration-200 absolute"
          onClick={onClick}
          disabled={isSpinning && buttonName === 'spinButton'}
          aria-label={buttonName.replace('Button', '')}
          style={{
            width: `${buttonSize}px`,
            height: `${buttonSize}px`,
            background: 'transparent',
            border: 'none',
            padding: '0',
            left: `${layoutInfo.x}px`,
            top: '50%',
            transform: 'translate(-50%, -50%)'
          }}
        >
          <img
            src={buttonImage}
            alt={buttonName}
            className="w-full h-full object-contain"
            style={{
              filter: 'drop-shadow(0 4px 6px rgba(0, 0, 0, 0.3))',
              borderRadius: metadata?.shape === 'circle' ? '50%' : 
                           metadata?.shape === 'rounded' ? '20%' : '0'
            }}
          />
        </button>
      );
    }
    
    return defaultContent;
  };

  // Render the spin button based on props
  const renderSpinButton = () => {
    if (buttonLayout.length > 0 && customButtons?.spinButton) {
      return renderAdaptiveButton('spinButton', () => {
        // Only call external onSpin if provided, otherwise use context spin
        if (onSpin) {
          onSpin();
        } else {
          spin();
        }
      });
    }
    
    if (customButtons?.spinButton) {
      // Fallback to fixed layout if no adaptive layout available
      return (
        <button 
          className="spin-btn flex items-center justify-center transform hover:scale-105 transition-all duration-200 relative"
          onClick={() => {
            // Only call external onSpin if provided, otherwise use context spin
            if (onSpin) {
              onSpin();
            } else {
              spin();
            }
          }}
          disabled={isSpinning}
          aria-label="Spin"
          style={{ 
            width: '90px', 
            height: '90px',
            background: 'transparent',
            border: 'none',
            padding: '0',
            zIndex: 200, // High z-index for spin button
            position: 'relative'
          }}
        >
          <img 
            src={customButtons.spinButton} 
            alt="Spin" 
            className="w-full h-full object-contain" 
            style={{ 
              filter: 'drop-shadow(0 4px 6px rgba(0, 0, 0, 0.3))',
              maxWidth: '100%',
              maxHeight: '100%'
            }}
            onError={(e) => {
              console.error('[SlotGameUI] Failed to load spin button:', customButtons.spinButton);
              e.currentTarget.style.display = 'none';
            }}
            onLoad={() => console.log('[SlotGameUI] Spin button loaded successfully')}
          />
        </button>
      );
    } else if (spinButtonSvg) {
      // Custom SVG spin button (if provided)
      return (
        <button 
          className="spin-btn bg-white text-black rounded-full w-[80px] h-[80px] flex items-center justify-center shadow-lg transform hover:scale-105 transition-all duration-200 relative group"
          onClick={() => {
            // Only call external onSpin if provided, otherwise use context spin
            if (onSpin) {
              onSpin();
            } else {
              spin();
            }
          }}
          disabled={isSpinning}
          aria-label="Spin"
        >
          <div className="relative z-10 w-12 h-12 flex items-center justify-center" dangerouslySetInnerHTML={{ __html: spinButtonSvg }} />
        </button>
      );
    } else if (spinButtonImageUrl) {
      // Image-based spin button (if provided)
      return (
        <button 
          className="spin-btn bg-white text-black rounded-full w-[80px] h-[80px] flex items-center justify-center shadow-lg transform hover:scale-105 transition-all duration-200 relative group"
          onClick={() => {
            // Only call external onSpin if provided, otherwise use context spin
            if (onSpin) {
              onSpin();
            } else {
              spin();
            }
          }}
          disabled={isSpinning}
          aria-label="Spin"
        >
          <div className="relative z-10 w-14 h-14 flex items-center justify-center">
            <img src={spinButtonImageUrl} alt="Spin" className="w-12 h-12 object-contain" />
          </div>
        </button>
      );
    } else {
      // Default white circular button with black arrow
      return (
        <button 
          className={`spin-btn bg-gradient-to-b from-yellow-400 to-yellow-600 text-black rounded-full w-[80px] h-[80px] flex items-center justify-center shadow-lg transform hover:scale-105 transition-all duration-200 relative border-2 border-yellow-700 ${
            isSpinning ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer hover:from-yellow-300 hover:to-yellow-500'
          }`}
          style={{ zIndex: 200, position: 'relative', pointerEvents: 'auto' }} // High z-index for spin button
          onClick={() => {
            console.log('[SlotGameUI] Spin button clicked - isSpinning:', isSpinning, 'balance:', gameBalance, 'bet:', gameBet);
            if (!isSpinning && gameBalance >= gameBet) {
              // Only call external onSpin if provided, otherwise use context spin
              if (onSpin) {
                onSpin();
              } else {
                spin();
              }
            } else {
              console.log('[SlotGameUI] Spin blocked - insufficient balance or already spinning');
            }
          }}
          disabled={isSpinning}
          aria-label="Spin"
          title={isSpinning ? 'Spinning...' : `Spin (Balance: ${gameBalance}, Bet: ${gameBet})`}
        >
          <div className="flex items-center justify-center">
            {/* Black arrow icon */}
            <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={3}>
              <path strokeLinecap="round" strokeLinejoin="round" d="M13 7l5 5m0 0l-5 5m5-5H6" />
            </svg>
          </div>
        </button>
      );
    }
  };

  // Render slam stop buttons
  const renderSlamStopButtons = () => {
    if (!showSlamStop || !isSpinning) return null;

    const numReels = Math.max(5, ...Object.keys(slamStopStatus).map(k => parseInt(k) + 1));
    
    return (
      <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-full mb-2 flex gap-2 z-50">
        {/* Slam Stop All Button */}
        <button
          className="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-xs font-bold border border-red-400 shadow-lg transition-all duration-150"
          onClick={() => onSlamStop?.()}
          disabled={!Object.values(slamStopStatus).some(status => status.canStop)}
          title="Slam Stop All Reels"
        >
          🛑 STOP ALL
        </button>

        {/* Individual Reel Slam Stop Buttons */}
        {Array.from({ length: numReels }, (_, index) => {
          const status = slamStopStatus[index] || { canStop: false, isSpinning: false, isStopped: false };
          const canStop = status.canStop;
          const isStopped = status.isStopped;
          
          return (
            <button
              key={index}
              className={`px-2 py-1 rounded text-xs font-bold border shadow-lg transition-all duration-150 ${
                isStopped 
                  ? 'bg-gray-500 text-gray-300 border-gray-400 cursor-not-allowed'
                  : canStop 
                    ? 'bg-yellow-600 hover:bg-yellow-700 text-white border-yellow-400' 
                    : 'bg-gray-400 text-gray-600 border-gray-300 cursor-not-allowed'
              }`}
              onClick={() => canStop && onSlamStop?.(index)}
              disabled={!canStop || isStopped}
              title={
                isStopped 
                  ? `Reel ${index + 1}: Stopped` 
                  : canStop 
                    ? `Slam Stop Reel ${index + 1}` 
                    : `Reel ${index + 1}: Not ready`
              }
            >
              {isStopped ? '✓' : canStop ? '🎯' : '⏳'} {index + 1}
            </button>
          );
        })}
      </div>
    );
  };

  // Logo drag handlers for interactive positioning
  const handleLogoMouseDown = useCallback((e: React.MouseEvent) => {
    if (!logoPositioningMode || !onLogoPositionChange) return;
    
    e.preventDefault();
    e.stopPropagation();
    
    setIsDraggingLogo(true);
    setDragStart({
      x: e.clientX,
      y: e.clientY
    });
    setStartPosition({
      x: deviceLogoPosition.x,
      y: deviceLogoPosition.y
    });
    
    console.log('[SlotGameUI] Logo drag started at:', { x: e.clientX, y: e.clientY });
  }, [logoPositioningMode, onLogoPositionChange, deviceLogoPosition]);

  const handleLogoMouseMove = useCallback((e: MouseEvent) => {
    if (!isDraggingLogo || !onLogoPositionChange) return;
    
    const deltaX = e.clientX - dragStart.x;
    const deltaY = e.clientY - dragStart.y;
    
    const newPosition = {
      x: startPosition.x + deltaX,
      y: startPosition.y + deltaY
    };
    
    // Constrain to reasonable bounds
    newPosition.x = Math.max(-400, Math.min(400, newPosition.x));
    newPosition.y = Math.max(-300, Math.min(300, newPosition.y));
    
    onLogoPositionChange(newPosition);
  }, [isDraggingLogo, dragStart, startPosition, onLogoPositionChange]);

  const handleLogoMouseUp = useCallback(() => {
    if (isDraggingLogo) {
      console.log('[SlotGameUI] Logo drag ended');
      setIsDraggingLogo(false);
    }
  }, [isDraggingLogo]);

  // Set up global mouse event listeners for logo dragging
  useEffect(() => {
    if (isDraggingLogo) {
      document.addEventListener('mousemove', handleLogoMouseMove);
      document.addEventListener('mouseup', handleLogoMouseUp);
      document.body.style.cursor = 'grabbing';
      
      return () => {
        document.removeEventListener('mousemove', handleLogoMouseMove);
        document.removeEventListener('mouseup', handleLogoMouseUp);
        document.body.style.cursor = '';
      };
    }
  }, [isDraggingLogo, handleLogoMouseMove, handleLogoMouseUp]);

  return (
    <div 
      ref={containerRef}
      className="flex flex-col relative"
      style={{ 
        position: 'relative',
        overflow: 'visible' // Always allow overflow for spin button
      }}
    >
      {/* Positioning mode overlay */}
      {logoPositioningMode && (
        <div className="absolute inset-0 z-10 pointer-events-none">
          {/* Grid overlay for positioning reference */}
          <svg
            className="absolute inset-0 w-full h-full opacity-20"
            style={{ pointerEvents: 'none' }}
          >
            <defs>
              <pattern
                id="positioning-grid"
                width="40"
                height="40"
                patternUnits="userSpaceOnUse"
              >
                <path
                  d="M 40 0 L 0 0 0 40"
                  fill="none"
                  stroke="#3B82F6"
                  strokeWidth="1"
                />
              </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#positioning-grid)" />
          </svg>
          
          {/* Center reference lines */}
          <div
            className="absolute bg-blue-400 opacity-40"
            style={{
              left: '50%',
              top: '0',
              width: '2px',
              height: '100%',
              transform: 'translateX(-50%)',
              pointerEvents: 'none'
            }}
          />
          <div
            className="absolute bg-blue-400 opacity-40"
            style={{
              left: '0',
              top: '50%',
              width: '100%',
              height: '2px',
              transform: 'translateY(-50%)',
              pointerEvents: 'none'
            }}
          />
        </div>
      )}


      {/* Positioning mode instructions */}
      {logoPositioningMode && gameLogo && (
        <div className="absolute bottom-4 left-4 right-4 z-30 pointer-events-none">
          <div className="bg-blue-600 bg-opacity-90 text-white text-xs px-3 py-2 rounded-md text-center">
            🎯 Positioning Mode ({currentDevice}): Drag the logo to position it
          </div>
        </div>
      )}
      
      {/* Main UI Bar - Full width horizontal bar with customizable style */}
      <div 
        data-testid="slot-ui"
        className={`slot-game-ui w-full flex justify-between items-center px-3 text-white relative ${className}`}
        style={{
          backgroundColor: config?.uiBar ? 'transparent' : (config?.uiCustomization?.barBackgroundColor || '#000000ff'),
          backgroundImage: config?.uiBar ? `url(${config.uiBar})` : undefined,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
          opacity: (config?.uiCustomization?.barOpacity || 90) / 100,
          height: config?.uiCustomization?.barHeight === 'compact' ? '60px' : 
                  config?.uiCustomization?.barHeight === 'large' ? '100px' : '80px',
          color: config?.uiCustomization?.textColor || '#FFFFFF',
          zIndex: 100,
          pointerEvents: 'auto', // Enable interactions for UI bar
          overflow: 'visible' // Allow spin button to extend above UI bar
        }}
      >
        {/* Slam Stop Buttons */}
        {renderSlamStopButtons()}
        {/* Check if we have adaptive layout */}
        {buttonLayout.length > 0 && customButtons ? (
          // Adaptive layout mode
          <div className="w-full h-full relative" style={{ position: 'relative' }}>
            {/* Balance and Bet Info - Left Side */}
            <div className="absolute left-4 top-1/2 transform -translate-y-1/2 flex items-center gap-6">
              <div className="flex flex-col items-center">
                <span className="text-xs font-medium uppercase tracking-wide">BALANCE</span>
                <span className="font-bold text-base">{formatCurrency(gameBalance)}</span>
              </div>
              <div className="flex flex-col items-center">
                <span className="text-xs font-medium uppercase tracking-wide">BET</span>
                <span className="font-bold text-base">{formatCurrency(gameBet)}</span>
              </div>
            </div>
            
            {/* Win Display - Right Side */}
            <div className="absolute right-4 top-1/2 transform -translate-y-1/2 flex flex-col items-center">
              <span className="text-xs font-medium uppercase tracking-wide">WIN</span>
              <span className="font-bold text-base">{formatCurrency(gameWin)}</span>
            </div>
            
            {/* Render all adaptive buttons */}
            {buttonLayout.map((layoutInfo) => (
              <AdaptiveButton
                key={layoutInfo.name}
                layoutInfo={layoutInfo}
                metadata={buttonMetadata[layoutInfo.name]}
                buttonImage={customButtons[layoutInfo.name]}
                pressedImage={config?.uiElementsPressed?.[layoutInfo.name]}
                isSpinning={isSpinning}
                onSpin={layoutInfo.name === 'spinButton' ? () => { spin(); onSpin?.(); } : undefined}
                onAutoplayToggle={layoutInfo.name === 'autoplayButton' ? () => { toggleAutoplay(); onAutoplayToggle?.(); } : undefined}
                onMenu={layoutInfo.name === 'menuButton' ? toggleMenu : undefined}
                onSound={layoutInfo.name === 'soundButton' ? toggleSound : undefined}
                onSettings={layoutInfo.name === 'settingsButton' ? toggleSettings : undefined}
              />
            ))}
          </div>
        ) : (
          // Original fixed layout as fallback
          <>
        {/* Left Section */}
        <div className="flex items-center gap-4">
          {/* Hamburger Menu Icon */}
          <button 
            className="cursor-pointer flex items-center justify-center"
            onClick={toggleMenu}
            style={{ 
              width: '50px', 
              height: '50px',
              background: 'transparent',
              border: 'none',
              padding: '0'
            }}
          >
            {customButtons?.menuButton ? (
              <img 
                src={customButtons.menuButton} 
                alt="Menu" 
                className="w-full h-full object-contain"
                style={{ 
                  filter: 'drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3))'
                }}
                onError={(e) => {
                  console.error('[SlotGameUI] Failed to load menu button:', customButtons.menuButton);
                  e.currentTarget.style.display = 'none';
                }}
              />
            ) : (
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            )}
          </button>
          
          {/* Info Button */}
          <div className="cursor-pointer">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          
          {/* Bet Section - Stacked */}
          <div className="flex flex-col items-center">
            <span className="text-xs font-medium uppercase tracking-wide">BET (FF)</span>
            <span className="font-bold text-base">{formatCurrency(gameBet)}</span>
          </div>
        </div>
        
        {/* Center Section - Spin Controls (Centered) */}
        <div className="absolute left-1/2 transform -translate-x-1/2 flex items-center gap-4" style={{ top: '50%', transform: 'translate(-50%, -50%)' }}>
          {/* Auto Spin Button */}
          <button 
            className={`flex flex-col items-center gap-1 cursor-pointer ${isAutoplayActive ? 'text-yellow-400' : ''}`}
            onClick={() => {
              toggleAutoplay();
              onAutoplayToggle?.();
            }}
            aria-label="Auto Spin"
          >
            {customButtons?.autoplayButton ? (
              <>
                <div
                  style={{
                    width: '50px',
                    height: '50px',
                    backgroundColor: 'transparent',
                    border: 'none',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}
                >
                  <img 
                    src={customButtons.autoplayButton} 
                    alt="Autoplay" 
                    style={{
                      width: '100%',
                      height: '100%',
                      objectFit: 'contain',
                      filter: 'drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3))'
                    }}
                    onError={(e) => {
                      console.error('[SlotGameUI] Failed to load autoplay button:', customButtons.autoplayButton);
                      e.currentTarget.style.display = 'none';
                    }}
                  />
                </div>
                <span className="text-xs">AUTO</span>
              </>
            ) : (
              <>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                  <path strokeLinecap="round" strokeLinejoin="round" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                <span className="text-xs">AUTO</span>
              </>
            )}
          </button>
          
          {/* Spin Button (centered) */}
          {renderSpinButton()}
          
          {/* Quick Spin Button */}
          <button 
            className="flex flex-col items-center gap-1 cursor-pointer"
            onClick={onMaxBet}
            aria-label="Quick Spin"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
              <path strokeLinecap="round" strokeLinejoin="round" d="M11.933 12.8a1 1 0 000-1.6L6.6 7.2A1 1 0 005 8v8a1 1 0 001.6.8l5.333-4zM19.933 12.8a1 1 0 000-1.6l-5.333-4A1 1 0 0013 8v8a1 1 0 001.6.8l5.333-4z" />
            </svg>
            <span className="text-xs">QUICK</span>
          </button>
        </div>
        
        {/* Right Section */}
        <div className="flex items-center gap-4">
          {/* Win Display - Stacked */}
          {/* <div className="flex flex-col items-center">
            <div className="text-xs font-medium uppercase">WIN (FF)</div>
            <div className="font-bold text-sm text-yellow-400">{formatCurrency(gameWin)}</div>
          </div> */}
          
          {/* Balance Display - Stacked */}
          <div className="flex flex-col items-center">
            <div className="text-xs font-medium uppercase">BALANCE (FF)</div>
            <div className="font-bold text-sm">{formatCurrency(gameBalance)}</div>
          </div>
          
          {/* Sound/Mute Button */}
          <button 
            className="cursor-pointer"
            onClick={toggleSound}
            style={{
              width: '50px',
              height: '50px',
              backgroundColor: 'transparent',
              border: 'none',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          >
            {customButtons?.soundButton ? (
              <img 
                src={customButtons.soundButton} 
                alt="Sound" 
                style={{
                  width: '100%',
                  height: '100%',
                  objectFit: 'contain',
                  filter: 'drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3))'
                }}
                onError={(e) => {
                  console.error('[SlotGameUI] Failed to load sound button:', customButtons.soundButton);
                  e.currentTarget.style.display = 'none';
                }}
              />
            ) : (
              <svg xmlns="http://www.w3.org/2000/svg" className={`h-6 w-6 ${!isSoundEnabled ? 'opacity-50' : ''}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={isSoundEnabled ? "M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z" : "M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15zM17 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2"} />
              </svg>
            )}
          </button>
          
          {/* Settings Button */}
          <button 
            className="cursor-pointer"
            onClick={toggleSettings}
            style={{
              width: '50px',
              height: '50px',
              backgroundColor: 'transparent',
              border: 'none',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          >
            {customButtons?.settingsButton ? (
              <img 
                src={customButtons.settingsButton} 
                alt="Settings" 
                style={{
                  width: '100%',
                  height: '100%',
                  objectFit: 'contain',
                  filter: 'drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3))'
                }}
                onError={(e) => {
                  console.error('[SlotGameUI] Failed to load settings button:', customButtons.settingsButton);
                  e.currentTarget.style.display = 'none';
                }}
              />
            ) : (
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
            )}
          </button>
        </div>
          </>
        )}
      </div>
      
      {/* Bottom Label - Left Aligned with GameCrafter branding */}
      <div className="text-white text-xs flex items-center gap-2 pl-6 py-1 bg-gray-900 border-t border-gray-800">
        <img 
          src="/assets/brand/logo-small.svg" 
          alt="Game Crafter Logo" 
          className="h-4 w-auto mr-2 invert" /* invert makes it white */
        />
        <span className="font-semibold">Premium Game | Game Crafter</span>
      </div>

      {/* Settings Modal */}
      {state.showSettings && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg p-6 max-w-md w-full">
            <h2 className="text-white text-xl font-bold mb-4">Game Settings</h2>
            
            {/* Sound Volume */}
            <div className="mb-4">
              <label className="text-white text-sm mb-2 block">Sound Volume</label>
              <input 
                type="range" 
                min="0" 
                max="100" 
                value={state.volume} 
                onChange={(e) => {
                  if (contextData) {
                    dispatch({ type: 'SET_VOLUME', payload: parseInt(e.target.value) });
                  } else {
                    setLocalState(prev => ({ ...prev, volume: parseInt(e.target.value) }));
                  }
                }}
                className="w-full"
              />
            </div>

            {/* Turbo Mode */}
            <div className="mb-4">
              <label className="text-white flex items-center">
                <input 
                  type="checkbox" 
                  checked={state.turboMode || false}
                  onChange={() => setTurboMode(!state.turboMode)}
                  className="mr-2"
                />
                Turbo Mode
              </label>
            </div>

            {/* Show Animations */}
            <div className="mb-4">
              <label className="text-white flex items-center">
                <input 
                  type="checkbox" 
                  checked={state.showAnimations}
                  onChange={() => {
                    if (contextData) {
                      dispatch({ type: 'TOGGLE_ANIMATIONS' });
                    } else {
                      setLocalState(prev => ({ ...prev, showAnimations: !prev.showAnimations }));
                    }
                  }}
                  className="mr-2"
                />
                Show Win Animations
              </label>
            </div>

            <button 
              onClick={toggleSettings}
              className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 w-full"
            >
              Close
            </button>
          </div>
        </div>
      )}

      {/* Menu Modal */}
      {state.showMenu && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg p-6 max-w-md w-full">
            <h2 className="text-white text-xl font-bold mb-4">Game Menu</h2>
            
            <div className="space-y-3">
              <button className="w-full bg-gray-700 text-white py-3 rounded hover:bg-gray-600 text-left px-4">
                Game Rules
              </button>
              <button className="w-full bg-gray-700 text-white py-3 rounded hover:bg-gray-600 text-left px-4">
                Paytable
              </button>
              <button className="w-full bg-gray-700 text-white py-3 rounded hover:bg-gray-600 text-left px-4">
                Game History
              </button>
              <button className="w-full bg-gray-700 text-white py-3 rounded hover:bg-gray-600 text-left px-4">
                Help & Support
              </button>
            </div>

            <button 
              onClick={toggleMenu}
              className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 w-full mt-4"
            >
              Close
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default SlotGameUI;