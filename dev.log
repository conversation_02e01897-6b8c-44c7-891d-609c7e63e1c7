
> slotai@0.1.0 dev
> npx vite

failed to load config from /mnt/c/CodexCli/slotai/vite.config.ts
error when starting dev server:
Error [ERR_MODULE_NOT_FOUND]: Cannot find package '/mnt/c/CodexCli/slotai/node_modules/vite/' imported from /mnt/c/CodexCli/slotai/node_modules/.vite-temp/vite.config.ts.timestamp-1750712590487-325c0865a2899.mjs
    at new NodeError (node:internal/errors:405:5)
    at legacyMainResolve (node:internal/modules/esm/resolve:218:9)
    at packageResolve (node:internal/modules/esm/resolve:903:14)
    at moduleResolve (node:internal/modules/esm/resolve:973:20)
    at defaultResolve (node:internal/modules/esm/resolve:1206:11)
    at ModuleLoader.defaultResolve (node:internal/modules/esm/loader:404:12)
    at ModuleLoader.resolve (node:internal/modules/esm/loader:373:25)
    at ModuleLoader.getModuleJob (node:internal/modules/esm/loader:250:38)
    at ModuleWrap.<anonymous> (node:internal/modules/esm/module_job:76:39)
    at link (node:internal/modules/esm/module_job:75:36)
